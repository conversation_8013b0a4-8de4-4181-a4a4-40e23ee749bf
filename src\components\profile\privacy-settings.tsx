'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/components/ui/use-toast';
import { 
  getUserConsents, 
  getUserConsentByType, 
  createConsent, 
  updateConsent,
  getConsentVersions,
  createPrivacyConsentTable,
} from '@/app/actions/privacy';
import type { ConsentFormData } from '@/lib/schemas/privacy';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Info, Shield, CheckCircle2, AlertTriangle, Lock, ExternalLink, RefreshCcw } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

// Type for consent response to ensure type safety
type PrivacySetupResponse = 
  | { success: true; message?: string }
  | { error: string; adminAction?: boolean; details?: string };

type ConsentStatus = {
  id?: string;
  consentType: ConsentFormData['consentType'];
  name: string;
  description: string;
  consentGiven: boolean;
  consentVersion: string;
  consentText: string;
  hasConsent: boolean;
  lastUpdated?: string;
};

export function PrivacySettings() {
  const [consents, setConsents] = useState<ConsentStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedConsent, setSelectedConsent] = useState<ConsentStatus | null>(null);
  const [setupRequired, setSetupRequired] = useState(false);
  const [adminActionRequired, setAdminActionRequired] = useState(false);
  const [adminInstructions, setAdminInstructions] = useState<string>('');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const [resetInProgress, setResetInProgress] = useState(false);

  useEffect(() => {
    const fetchConsentsData = async () => {
      setLoading(true);
      setErrorMessage('');
      
      try {
        // Get available consent versions
        const { versions, error: versionsError } = await getConsentVersions();
        
        // Check if tables need to be set up
        if (versionsError) {
          if (versionsError.includes('does not exist')) {
            setSetupRequired(true);
            setLoading(false);
            return;
          }
          
          console.warn(`Error fetching consent versions: ${versionsError}`);
          setErrorMessage(`Failed to load privacy settings: ${versionsError}`);
        }

        // Create default consent statuses from available versions or use defaults if none
        const consentStatuses: ConsentStatus[] = getConsentStatuses(versions);

        // Get user's consents to update status
        const { consents: userConsents, error: consentsError } = await getUserConsents();
        
        if (consentsError && !consentsError.includes('does not exist')) {
          console.warn(`Error fetching user consents: ${consentsError}`);
          setErrorMessage(`Failed to load your consent history: ${consentsError}`);
        }

        // Map user consents to their types
        const updatedStatuses = mapUserConsentsToStatuses(consentStatuses, userConsents);
        setConsents(updatedStatuses);
      } catch (error) {
        console.error("Error fetching privacy data:", error);
        setErrorMessage(error instanceof Error ? error.message : "Failed to load privacy settings");
        toast({
          title: "Error",
          description: "Failed to load privacy settings. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    fetchConsentsData();
  }, []);

  // Helper function to get consent statuses from versions or defaults
  const getConsentStatuses = (versions: unknown[] | undefined): ConsentStatus[] => {
    if (!versions || versions.length === 0) {
      return getDefaultConsentTypes().map(type => ({
        consentType: type.consentType as ConsentFormData['consentType'],
        name: type.name,
        description: type.description,
        consentGiven: false,
        consentVersion: type.version,
        consentText: type.consentText,
        hasConsent: false,
      }));
    }
    
    return versions.map(version => {
      const consentType = version.name.toLowerCase().replace(/\s+/g, '') as ConsentFormData['consentType'];
      return {
        consentType,
        name: version.name,
        description: version.description || '',
        consentGiven: false,
        consentVersion: version.version,
        consentText: version.consent_text,
        hasConsent: false,
      };
    });
  };

  // Helper function to map user consents to statuses
  const mapUserConsentsToStatuses = (statuses: ConsentStatus[], userConsents: unknown[] | undefined): ConsentStatus[] => {
    if (!userConsents || userConsents.length === 0) {
      return statuses;
    }
    
    const userConsentMap = new Map();
    userConsents.forEach(consent => {
      if (!userConsentMap.has(consent.consent_type) || 
          new Date(consent.created_at) > new Date(userConsentMap.get(consent.consent_type).created_at)) {
        userConsentMap.set(consent.consent_type, consent);
      }
    });

    return statuses.map(status => {
      const userConsent = userConsentMap.get(status.consentType);
      if (userConsent) {
        return {
          ...status,
          id: userConsent.id,
          consentGiven: userConsent.consent_given,
          hasConsent: true,
          lastUpdated: userConsent.updated_at,
        };
      }
      return status;
    });
  };

  // Default consent types when none available from the database
  const getDefaultConsentTypes = () => [
    {
      name: 'Marketing',
      consentType: 'marketing',
      description: 'Receive marketing communications such as newsletters, product updates, event invitations, and other promotional content via email or other channels.',
      consentText: 'I agree to receive marketing communications, newsletters, and updates about events and services.',
      version: '1.0'
    },
    {
      name: 'Analytics',
      consentType: 'analytics',
      description: 'Allow collection and analysis of your browsing behavior, app usage, and interaction patterns to improve our services and user experience.',
      consentText: 'I agree to the collection and processing of my usage data for analytics and service improvement purposes.',
      version: '1.0'
    },
    {
      name: 'Third Party',
      consentType: 'thirdpartysharing',
      description: 'Share your data with trusted third-party partners for service delivery, integration with other platforms, and improving our offerings.',
      consentText: 'I agree to share my data with trusted third-party partners for service delivery and improvement.',
      version: '1.0'
    },
    {
      name: 'Data Sharing',
      consentType: 'datasharing',
      description: 'Allow sharing of your profile information with event organizers for registration, ticketing, and event management purposes.',
      consentText: 'I agree to share my profile information with event organizers for event registration and management.',
      version: '1.0'
    },
    {
      name: 'Profile Display',
      consentType: 'profiledisplay',
      description: 'Make your profile visible in public listings such as event attendees, community directories, and other user collections.',
      consentText: 'I agree to display my profile information in public event attendee listings.',
      version: '1.0'
    },
    {
      name: 'Cookie Preferences',
      consentType: 'cookiepreferences',
      description: 'Control which types of cookies are set on your browser beyond essential cookies required for the site to function.',
      consentText: 'I agree to allow non-essential cookies to be stored on my device to enhance site functionality and personalization.',
      version: '1.0'
    }
  ];

  const toggleConsent = (consent: ConsentStatus) => {
    setSelectedConsent({
      ...consent,
      consentGiven: !consent.consentGiven
    });
    setDialogOpen(true);
  };

  const handleConfirmConsent = async () => {
    if (!selectedConsent) return;

    try {
      const formData: ConsentFormData = {
        consentType: selectedConsent.consentType,
        consentGiven: selectedConsent.consentGiven,
        consentVersion: selectedConsent.consentVersion,
        consentText: selectedConsent.consentText,
      };

      let result;
      
      // Update or create consent
      if (selectedConsent.id) {
        result = await updateConsent(selectedConsent.id, formData);
      } else {
        result = await createConsent(formData);
      }

      if (result.error || !result.consent) {
        throw new Error(result.error || 'Failed to update consent');
      }

      // Update local state
      setConsents(prev => 
        prev.map(c => 
          c.consentType === selectedConsent.consentType ? 
          {
            ...c,
            id: result.consent.id,
            consentGiven: selectedConsent.consentGiven,
            hasConsent: true,
            lastUpdated: result.consent.updated_at
          } : c
        )
      );

      toast({
        title: "Privacy settings updated",
        description: selectedConsent.consentGiven 
          ? `You have opted in to ${selectedConsent.name}` 
          : `You have opted out of ${selectedConsent.name}`,
      });
    } catch (error) {
      console.error("Error updating consent:", error);
      toast({
        title: "Error",
        description: "Failed to update privacy settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setDialogOpen(false);
      setSelectedConsent(null);
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return "";
    
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const handleSetupPrivacy = async () => {
    try {
      setLoading(true);
      const response = await createPrivacyConsentTable() as PrivacySetupResponse;
      
      if ('error' in response && response.error) {
        // Type-safe approach to check for adminAction and details
        if ('adminAction' in response && response.adminAction === true && 'details' in response) {
          setAdminActionRequired(true);
          setAdminInstructions(response.details || '');
        } else {
          toast({
            title: "Error",
            description: response.error,
            variant: "destructive",
          });
        }
      }
      
      if ('success' in response && response.success) {
        toast({
          title: "Success",
          description: "Privacy settings have been set up successfully.",
        });
        // Reload the page to show the new privacy settings
        window.location.reload();
      }
    } catch (error) {
      console.error("Error setting up privacy settings:", error);
      toast({
        title: "Error",
        description: "Failed to set up privacy settings. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleResetAllConsents = async () => {
    try {
      setResetInProgress(true);
      
      // Process each consent and create a new record with consent_given = false
      for (const consent of consents) {
        if (consent.id) {
          const formData: ConsentFormData = {
            consentType: consent.consentType,
            consentGiven: false, // Reset to false (opt-out)
            consentVersion: consent.consentVersion,
            consentText: consent.consentText,
          };
          
          await updateConsent(consent.id, formData);
        }
      }
      
      // Update local state
      setConsents(prev => 
        prev.map(c => ({
          ...c,
          consentGiven: false,
        }))
      );
      
      toast({
        title: "Privacy settings reset",
        description: "All your privacy settings have been reset. You are now opted out of all consent options.",
      });
    } catch (error) {
      console.error("Error resetting consents:", error);
      toast({
        title: "Error",
        description: "Failed to reset privacy settings. Please try again.",
        variant: "destructive",
      });
    } finally {
      setResetInProgress(false);
      setResetDialogOpen(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3, 4, 5].map((item) => (
          <Card key={item} className=&quot;animate-pulse">
            <CardContent className="p-6 flex items-center justify-between">
              <div className=&quot;space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className=&quot;h-3 w-64" />
              </div>
              <Skeleton className="h-6 w-11 rounded-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (setupRequired) {
    return (
      <div className=&quot;space-y-6">
        <Card className="border-amber-200 border">
          <CardHeader className=&quot;pb-2">
            <div className="flex items-center gap-2">
              <Shield className=&quot;h-5 w-5 text-amber-500" />
              <CardTitle>Privacy Settings Setup Required</CardTitle>
            </div>
            <CardDescription>
              The privacy settings database tables need to be set up
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="py-2">
              <p className=&quot;text-sm text-muted-foreground mb-4">
                To manage your privacy preferences, we need to set up the privacy tables in our database. 
                This will allow you to control how your data is used.
              </p>
              <Button 
                onClick={handleSetupPrivacy}
                disabled={adminActionRequired}
                className="mb-2"
              >
                Setup Privacy Settings
              </Button>
            </div>
          </CardContent>
        </Card>
        
        {adminActionRequired && (
          <Card className=&quot;border-red-200 border">
            <CardHeader className="pb-2">
              <div className=&quot;flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-500" />
                <CardTitle className=&quot;text-red-600">Administrator Action Required</CardTitle>
              </div>
              <CardDescription>
                Database setup requires administrator privileges
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className=&quot;text-sm text-muted-foreground">
                  The following steps need to be performed by an administrator:
                </p>
                <div className="bg-slate-50 p-4 rounded-md border text-xs font-mono">
                  <pre className=&quot;whitespace-pre-wrap">{adminInstructions}</pre>
                </div>
                <p className="text-sm text-muted-foreground">
                  Please contact your system administrator for assistance.
                </p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  }

  return (
    <div className=&quot;space-y-6">
      <div className="bg-blue-50 border border-blue-100 rounded-lg p-4 mb-6 flex items-start gap-3">
        <Lock className=&quot;h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0" />
        <div>
          <h3 className="font-medium text-blue-800 mb-1">Your Privacy Matters</h3>
          <p className=&quot;text-sm text-blue-700">
            Control how your information is used. You can opt in or out at any time, and we&apos;ll keep a record of your choices.
          </p>
        </div>
      </div>

      {errorMessage && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
          <div className=&quot;flex items-center gap-2 text-amber-800 mb-1">
            <AlertTriangle className="h-4 w-4" />
            <h3 className=&quot;font-medium">Warning</h3>
          </div>
          <p className="text-sm text-amber-700">
            {errorMessage}
          </p>
        </div>
      )}

      <div className=&quot;flex justify-between items-center mb-4">
        <div>
          <h2 className="text-xl font-semibold">Privacy Preferences</h2>
          <p className=&quot;text-sm text-muted-foreground">Manage how your data is collected and used</p>
        </div>
        <Button 
          variant="outline" 
          size=&quot;sm" 
          onClick={() =/> setResetDialogOpen(true)}
          disabled={consents.length === 0 || resetInProgress}
          className="flex items-center gap-1"
        >
          <RefreshCcw className=&quot;h-3.5 w-3.5 mr-1" />
          Reset All
        </Button>
      </div>

      {consents.length === 0 ? (
        <Card className="border-dashed border-yellow-300">
          <CardContent className=&quot;p-6">
            <div className="flex flex-col items-center justify-center text-center gap-4 py-6">
              <Shield className=&quot;h-12 w-12 text-yellow-500" />
              <h3 className="text-xl font-medium">No Privacy Settings Available</h3>
              <p className=&quot;text-muted-foreground max-w-md">
                No privacy consent options are currently available.
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4">
          {consents.map((consent) => (
            <Card 
              key={consent.consentType} 
              className={`transition-all ${consent.hasConsent ? '' : 'border-dashed'}`}
            >
              <CardContent className=&quot;p-6">
                <div className="flex flex-wrap items-center justify-between gap-4 mb-2">
                  <div className=&quot;flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className=&quot;font-medium">{consent.name}</h3>
                      {consent.hasConsent && (
                        <Badge variant={consent.consentGiven ? "default" : "outline"} className={`ml-1 ${consent.consentGiven ? 'bg-green-500 hover:bg-green-600' : ''}`}>
                          {consent.consentGiven ? "Opted In" : "Opted Out"}
                        </Badge>
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {consent.description || consent.consentText}
                    </p>
                  </div>
                  <Switch 
                    checked={consent.consentGiven} 
                    onCheckedChange={() => toggleConsent(consent)}
                    aria-label={`Toggle ${consent.name} consent`}
                  />
                </div>
                
                {consent.hasConsent && (
                  <div className=&quot;mt-3 pt-3 border-t text-xs text-muted-foreground flex justify-between">
                    <span>Last updated: {formatDate(consent.lastUpdated)}</span>
                    <span className="text-xs">Version {consent.consentVersion}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      <Separator className=&quot;my-8" />

      <div className="bg-white border border-slate-100 dark:bg-slate-900 rounded-lg p-4 flex flex-col sm:flex-row items-start gap-4 shadow-sm">
        <Shield className=&quot;h-6 w-6 text-blue-500 mt-0.5 flex-shrink-0" />
        <div className="w-full">
          <h3 className=&quot;font-medium mb-2 text-foreground dark:text-slate-100">Your Data Protection Rights</h3>
          <div className="grid gap-3 sm:grid-cols-2">
            <div className=&quot;text-sm border border-slate-200 rounded-md p-3 bg-slate-50 dark:bg-slate-800 dark:border-slate-700">
              <span className="font-medium block mb-1 text-slate-800 dark:text-slate-200">Right to Access</span>
              <p className=&quot;text-xs text-slate-600 dark:text-slate-400">
                Request a copy of your personal data
              </p>
            </div>
            <div className="text-sm border border-slate-200 rounded-md p-3 bg-slate-50 dark:bg-slate-800 dark:border-slate-700">
              <span className=&quot;font-medium block mb-1 text-slate-800 dark:text-slate-200">Right to Rectification</span>
              <p className="text-xs text-slate-600 dark:text-slate-400">
                Correct inaccurate information
              </p>
            </div>
            <div className=&quot;text-sm border border-slate-200 rounded-md p-3 bg-slate-50 dark:bg-slate-800 dark:border-slate-700">
              <span className="font-medium block mb-1 text-slate-800 dark:text-slate-200">Right to Erasure</span>
              <p className=&quot;text-xs text-slate-600 dark:text-slate-400">
                Request deletion of your data ("right to be forgotten")
              </p>
            </div>
            <div className="text-sm border border-slate-200 rounded-md p-3 bg-slate-50 dark:bg-slate-800 dark:border-slate-700">
              <span className=&quot;font-medium block mb-1 text-slate-800 dark:text-slate-200">Right to Data Portability</span>
              <p className="text-xs text-slate-600 dark:text-slate-400">
                Receive and transfer your data
              </p>
            </div>
          </div>
          <div className=&quot;mt-4 text-xs flex items-center gap-1 text-slate-500 dark:text-slate-400">
            <a href="/privacy-policy" className=&quot;inline-flex items-center hover:underline text-blue-600">
              View Full Privacy Policy
              <ExternalLink className="h-3 w-3 ml-1" />
            </a>
          </div>
        </div>
      </div>

      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Privacy Setting Change</DialogTitle>
            <DialogDescription>
              {selectedConsent?.consentGiven
                ? `Are you sure you want to opt in to ${selectedConsent?.name}?`
                : `Are you sure you want to opt out of ${selectedConsent?.name}?`}
            </DialogDescription>
          </DialogHeader>

          <div className=&quot;py-4">
            <div className={`p-3 rounded-md ${selectedConsent?.consentGiven ? 'bg-green-50 border border-green-100' : 'bg-gray-50 border'}`}>
              <p className="text-sm">
                {selectedConsent?.consentText}
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button variant=&quot;outline" onClick={() => setDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleConfirmConsent}>
              Confirm
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <AlertDialog open={resetDialogOpen} onOpenChange={setResetDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reset All Privacy Settings</AlertDialogTitle>
            <AlertDialogDescription>
              This will reset all your privacy preferences to "Opted Out". You&apos;ll need to opt in again 
              to any services you want to use. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleResetAllConsents} 
              disabled={resetInProgress}
              className="bg-red-600 hover:bg-red-700"
            >
              {resetInProgress ? "Resetting..." : "Reset All Settings"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
