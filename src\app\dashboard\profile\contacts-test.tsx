'use client';

import { useState, useEffect } from 'react';
import { getSavedContacts } from '@/app/actions/contacts';
import { Button } from '@/components/ui/button';
import { logger } from '@/lib/logger';

export default function ContactsTest() {
  const [result, setResult] = useState<unknown>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchContacts = async () => {
    setLoading(true);
    setError(null);
    try {
      if (process.env.NODE_ENV === 'development') {

        logger.debug('Fetching contacts...');

      }
      const response = await getSavedContacts();
      logger.info('Response:', response);
      setResult(response);
    } catch (err) {
      console.error('Error fetching contacts:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-md">
      <h2 className=&quot;text-xl font-bold mb-4">Contacts Test</h2>
      
      <Button 
        onClick={fetchContacts} 
        disabled={loading}
        className="mb-4"
      >
        {loading ? 'Loading...' : 'Fetch Contacts'}
      </Button>
      
      {error && (
        <div className=&quot;p-4 mb-4 bg-red-100 text-red-700 rounded-md">
          <p className="font-bold">Error:</p>
          <p>{error}</p>
        </div>
      )}
      
      {result && (
        <div className=&quot;mt-4">
          <h3 className="font-bold mb-2">Result:</h3>
          <pre className=&quot;p-4 bg-gray-100 rounded-md overflow-auto max-h-96">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
