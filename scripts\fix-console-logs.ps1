# PowerShell script to automatically replace console.log statements
# with proper logger calls in the Fuiyoo codebase

param(
    [switch]$DryRun = $true,
    [string]$SourceDir = "src"
)

Write-Host "🚀 Starting automated console.log replacement..." -ForegroundColor Green
Write-Host "📁 Source directory: $SourceDir" -ForegroundColor Cyan
Write-Host "🔍 Dry run mode: $($DryRun.ToString().ToUpper())" -ForegroundColor Cyan
Write-Host ""

# Statistics
$stats = @{
    FilesProcessed = 0
    FilesModified = 0
    ConsoleLogsReplaced = 0
    ImportsAdded = 0
    Errors = 0
}

# Get all TypeScript/JavaScript files
$files = Get-ChildItem -Path $SourceDir -Recurse -Include "*.ts", "*.tsx", "*.js", "*.jsx" | 
    Where-Object { $_.FullName -notmatch "node_modules|\.next|\.git|dist|build" }

Write-Host "📄 Found $($files.Count) files to check" -ForegroundColor Cyan
Write-Host ""

foreach ($file in $files) {
    $stats.FilesProcessed++
    
    try {
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        
        # Skip files without console.log
        if ($content -notmatch "console\.log\s*\(") {
            continue
        }
        
        Write-Host "Processing: $($file.FullName)" -ForegroundColor Yellow
        
        $originalContent = $content
        $hasChanges = $false
        $replacementCount = 0
        
        # Check if logger import exists
        $hasLoggerImport = $content -match "import.*logger.*from.*['""]@/lib/logger['""]"
        
        # Add logger import if needed
        if (-not $hasLoggerImport) {
            # Find the best place to insert import (after other imports)
            $lines = $content -split "`n"
            $insertIndex = 0
            
            for ($i = 0; $i -lt $lines.Length; $i++) {
                $line = $lines[$i].Trim()
                
                # Skip 'use client' and 'use server' directives
                if ($line -match "^['""]use (client|server)['""]") {
                    $insertIndex = $i + 1
                    continue
                }
                
                # If it's an import statement, update insert index
                if ($line -match "^import .* from") {
                    $insertIndex = $i + 1
                }
                
                # Stop at first non-import, non-comment, non-empty line
                if ($line -and $line -notmatch "^(import |//|/\*|\*)" -and $line -ne "") {
                    break
                }
            }
            
            # Insert logger import
            $lines = $lines[0..($insertIndex-1)] + "import { logger } from '@/lib/logger';" + $lines[$insertIndex..($lines.Length-1)]
            $content = $lines -join "`n"
            $hasChanges = $true
            $stats.ImportsAdded++
        }
        
        # Replace console.log statements
        $content = $content -replace "(?m)^(\s*)console\.log\((.*)\);?\s*$", {
            param($match)
            
            $indent = $match.Groups[1].Value
            $args = $match.Groups[2].Value
            $fullMatch = $match.Groups[0].Value
            
            # Skip if already wrapped in development check
            if ($originalContent -match "if \(process\.env\.NODE_ENV === 'development'\)" -and 
                $originalContent.IndexOf($fullMatch) -gt $originalContent.IndexOf("if (process.env.NODE_ENV === 'development')")) {
                return $fullMatch
            }
            
            # Skip console.error and console.warn
            if ($fullMatch -match "console\.(error|warn)") {
                return $fullMatch
            }
            
            $script:replacementCount++
            
            # Determine if this is a debug statement
            $isDebug = $args -match "\[DEBUG\]|debug|Debug|loading|fetching|checking"
            
            if ($isDebug) {
                return "$indent" + "if (process.env.NODE_ENV === 'development') {`n$indent  logger.debug($args);`n$indent}"
            } else {
                return "$indent" + "logger.info($args);"
            }
        }
        
        if ($script:replacementCount -gt 0) {
            $hasChanges = $true
            $stats.ConsoleLogsReplaced += $script:replacementCount
            Write-Host "  → Replaced $script:replacementCount console.log statements" -ForegroundColor Green
            $script:replacementCount = 0
        }
        
        # Write changes if not dry run
        if ($hasChanges) {
            if (-not $DryRun) {
                Set-Content -Path $file.FullName -Value $content -Encoding UTF8
                $stats.FilesModified++
                Write-Host "  ✅ Updated $($file.FullName)" -ForegroundColor Green
            } else {
                Write-Host "  🔍 Would update $($file.FullName) (dry run)" -ForegroundColor Magenta
            }
        }
        
    } catch {
        Write-Host "Error processing $($file.FullName): $($_.Exception.Message)" -ForegroundColor Red
        $stats.Errors++
    }
}

# Print summary
Write-Host ""
Write-Host "📊 SUMMARY:" -ForegroundColor Green
Write-Host "   Files processed: $($stats.FilesProcessed)" -ForegroundColor White
Write-Host "   Files modified: $($stats.FilesModified)" -ForegroundColor White
Write-Host "   Console.log statements replaced: $($stats.ConsoleLogsReplaced)" -ForegroundColor White
Write-Host "   Logger imports added: $($stats.ImportsAdded)" -ForegroundColor White
Write-Host "   Errors: $($stats.Errors)" -ForegroundColor White

if ($DryRun) {
    Write-Host ""
    Write-Host "🔍 This was a dry run. No files were actually modified." -ForegroundColor Yellow
    Write-Host "   Run with -DryRun:`$false to apply changes." -ForegroundColor Yellow
}
