'use client'

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { MoreHorizontal } from "lucide-react"
import { cn } from "@/lib/utils"

interface SimpleDropdownProps {
  children: React.ReactNode
  trigger?: React.ReactNode
  align?: "start" | "center" | "end"
  className?: string
}

export function SimpleDropdown({
  children,
  trigger = <MoreHorizontal className="h-4 w-4" />,
  align = &quot;end",
  className
}: SimpleDropdownProps) {
  const [open, setOpen] = React.useState(false)
  const dropdownRef = React.useRef<HTMLDivElement>(null)
  const buttonRef = React.useRef<HTMLButtonElement>(null)

  // Close the dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (e: MouseEvent) => {
      if (
        open &&
        dropdownRef.current &&
        buttonRef.current &&
        !dropdownRef.current.contains(e.target as Node) &&
        !buttonRef.current.contains(e.target as Node)
      ) {
        setOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [open])

  // Close the dropdown when Escape is pressed
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && open) {
        setOpen(false)
        buttonRef.current?.focus()
      }
    }

    document.addEventListener('keydown', handleEscape)
    return () => document.removeEventListener('keydown', handleEscape)
  }, [open])

  return (
    <div className={cn("relative", className)}>
      <Button
        ref={buttonRef}
        variant="ghost"
        size="icon"
        aria-label="Menu"
        aria-expanded={open}
        aria-haspopup="menu"
        onClick={() =/> setOpen(!open)}
        type=&quot;button"
      >
        {trigger}
      </Button>

      {open && (
        <div
          ref={dropdownRef}
          className={cn(
            &quot;absolute z-[100] min-w-[8rem] overflow-hidden rounded-md border border-border",
            "bg-popover text-popover-foreground p-1 shadow-md",
            "animate-in fade-in-0 zoom-in-95",
            "right-full mr-2" // Position to the left of the trigger
          )}
          style={{
            backgroundColor: 'hsl(var(--popover))',
            top: '50%',
            transform: 'translateY(-50%)' // Center vertically relative to the button
          }}
          role="menu"
          aria-orientation="vertical"
        >
          {children}
        </div>
      )}
    </div>
  )
}

interface SimpleDropdownItemProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  destructive?: boolean
}

export function SimpleDropdownItem({
  className,
  children,
  destructive = false,
  ...props
}: SimpleDropdownItemProps) {
  return (
    <button
      className={cn(
        &quot;relative flex w-full cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none",
        "transition-colors hover:bg-accent hover:text-accent-foreground",
        "focus-visible:bg-accent focus-visible:text-accent-foreground",
        "data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
        destructive && "text-destructive hover:bg-destructive/10",
        className
      )}
      role="menuitem"
      {...props}
    >
      {children}
    </button>
  )
}
