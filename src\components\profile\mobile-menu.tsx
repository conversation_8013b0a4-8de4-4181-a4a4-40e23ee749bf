'use client';
import React from 'react';
import { Menu, Mars, Venus, UserCircle, LayoutDashboard, Calendar, Users, Ticket, CreditCard } from 'lucide-react';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import Link from 'next/link';

interface MobileMenuProps {
  user: {
    first_name: string;
    last_name?: string;
    username?: string;
    gender?: string;
  };
  navigationItems: Array<{
    href: string;
    label: string;
    iconName: string;
  }>;
}

const iconMap = {
  UserCircle,
  LayoutDashboard,
  Calendar,
  Users,
  Ticket,
  CreditCard,
};

export function MobileMenu({ user, navigationItems }: MobileMenuProps) {
  const getGenderDisplay = (gender?: string) => {
    if (!gender) return null;
    const lowerGender = gender.toLowerCase();
    
    if (lowerGender === 'male') {
      return {
        icon: <Mars className="h-4 w-4" />,
        color: 'text-blue-500',
        bg: 'bg-blue-50'
      };
    }
    
    if (lowerGender === 'female') {
      return {
        icon: <Venus className=&quot;h-4 w-4" />,
        color: 'text-pink-500',
        bg: 'bg-pink-50'
      };
    }
    
    return null;
  };

  const getIcon = (iconName: string) => {
    const Icon = iconMap[iconName as keyof typeof iconMap];
    return Icon ? <Icon className="h-5 w-5 mr-3" /> : null;
  };

  const genderDisplay = getGenderDisplay(user.gender);

  return (
    <Sheet>
      <SheetTrigger asChild>
        <button className=&quot;lg:hidden p-2 hover:bg-accent rounded-md">
          <Menu className="h-5 w-5" />
          <span className=&quot;sr-only">Open menu</span>
        </button>
      </SheetTrigger>
      <SheetContent side="left" className="w-[300px] sm:w-[400px] p-0">
        <div className=&quot;flex flex-col h-full">
          {/* Profile Info */}
          <div className="text-center py-6 px-6 border-b">
            <h2 className=&quot;text-xl font-semibold mb-1">
              {user.first_name} {user.last_name}
            </h2>
            <div className="flex items-center justify-center gap-2">
              <span className=&quot;text-muted-foreground">@{user.username || "username"}</span>
              {genderDisplay && (
                <span className={`inline-flex items-center px-2 py-1 rounded-full ${genderDisplay.bg} ${genderDisplay.color}`}>
                  {genderDisplay.icon}
                </span>
              )}
            </div>
          </div>

          {/* Navigation */}
          <nav className="flex-1 py-6 px-3">
            <div className=&quot;space-y-1">
              {navigationItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-accent hover:text-accent-foreground"
                >
                  {getIcon(item.iconName)}
                  {item.label}
                </Link>
              ))}
            </div>
          </nav>
        </div>
      </SheetContent>
    </Sheet>
  );
} 
