import Link from 'next/link';

export default function AdminDashboardPage() {
  return (
    <div className="container mx-auto px-4">
      <h1 className=&quot;text-2xl font-semibold mb-6">Admin Overview</h1>

      {/* User Management */}
      <div className="mb-8 p-6 bg-card rounded-lg shadow border border-border">
        <h2 className=&quot;text-xl font-semibold mb-4">User Management</h2>
        <p className="text-muted-foreground mb-4">Manage users and their roles across the platform.</p>

        <div className=&quot;grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
          <div className="p-4 border border-border rounded-md bg-muted">
            <p className=&quot;font-medium">Total Users</p>
            <p className="text-2xl font-bold">0</p>
          </div>
          <div className=&quot;p-4 border border-border rounded-md bg-muted">
            <p className="font-medium">Event Organizers</p>
            <p className=&quot;text-2xl font-bold">0</p>
          </div>
          <div className="p-4 border border-border rounded-md bg-muted">
            <p className=&quot;font-medium">Pending Applications</p>
            <p className="text-2xl font-bold">0</p>
          </div>
        </div>

        <Link
          href=&quot;/admin/users"
          className="inline-flex items-center px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
        >
          Manage Users
        </Link>
      </div>

      {/* System Status */}
      <div className=&quot;p-6 bg-card rounded-lg shadow border border-border">
        <h2 className="text-xl font-semibold mb-4">System Status</h2>
        <p className=&quot;text-muted-foreground mb-4">Overview of system health and performance.</p>

        <div className="space-y-2">
          <div className=&quot;flex justify-between p-3 bg-success/10 text-success rounded-md">
            <span>API Services</span>
            <span className="font-medium">Operational</span>
          </div>
          <div className=&quot;flex justify-between p-3 bg-success/10 text-success rounded-md">
            <span>Database</span>
            <span className="font-medium">Operational</span>
          </div>
          <div className=&quot;flex justify-between p-3 bg-success/10 text-success rounded-md">
            <span>Auth Services</span>
            <span className="font-medium">Operational</span>
          </div>
        </div>
      </div>
    </div>
  );
}