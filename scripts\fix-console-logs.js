#!/usr/bin/env node

/**
 * Automated Console.log Replacement Script
 *
 * This script automatically replaces console.log statements with proper logger calls
 * following the established patterns in the Fuiyoo codebase.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  srcDir: './src',
  loggerImport: "import { logger } from '@/lib/logger';",
  extensions: ['.ts', '.tsx', '.js', '.jsx'],
  excludeDirs: ['node_modules', '.next', '.git', 'dist', 'build'],
  dryRun: false, // Set to false to apply changes
};

// Patterns to match and replace
const patterns = {
  // Console.log statements - more flexible pattern
  consoleLogPattern: /^(\s*)console\.log\s*\(([\s\S]*?)\)\s*;?\s*$/gm,

  // Import detection
  importPattern: /^import.*from\s+['"][^'"]*['"];?\s*$/gm,
  loggerImportPattern: /import.*logger.*from.*['"]@\/lib\/logger['"];?/,

  // React import pattern (to place logger import after React imports)
  reactImportPattern: /^import.*from\s+['"]react['"];?\s*$/gm,
};

// Statistics
let stats = {
  filesProcessed: 0,
  filesModified: 0,
  consoleLogsReplaced: 0,
  importsAdded: 0,
  errors: 0,
};

/**
 * Check if a file should be processed
 */
function shouldProcessFile(filePath) {
  // Check extension
  const ext = path.extname(filePath);
  if (!config.extensions.includes(ext)) return false;

  // Check if in excluded directory
  const relativePath = path.relative(config.srcDir, filePath);
  for (const excludeDir of config.excludeDirs) {
    if (relativePath.startsWith(excludeDir)) return false;
  }

  return true;
}

/**
 * Find all files to process
 */
function findFiles(dir) {
  let results = [];

  try {
    const files = fs.readdirSync(dir);

    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);

      if (stat.isDirectory()) {
        // Skip excluded directories
        if (!config.excludeDirs.includes(file)) {
          results = results.concat(findFiles(filePath));
        }
      } else if (shouldProcessFile(filePath)) {
        results.push(filePath);
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
    stats.errors++;
  }

  return results;
}

/**
 * Check if file already has logger import
 */
function hasLoggerImport(content) {
  return patterns.loggerImportPattern.test(content);
}

/**
 * Add logger import to file content
 */
function addLoggerImport(content) {
  if (hasLoggerImport(content)) {
    return content; // Already has import
  }

  const lines = content.split('\n');
  let insertIndex = 0;

  // Find the best place to insert the import
  // Look for the last import statement
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Skip 'use client' and 'use server' directives
    if (line.startsWith("'use ") || line.startsWith('"use ')) {
      insertIndex = i + 1;
      continue;
    }

    // If it's an import statement, update insert index
    if (line.startsWith('import ') && line.includes('from')) {
      insertIndex = i + 1;
    }

    // Stop at first non-import, non-comment, non-empty line
    if (line && !line.startsWith('import ') && !line.startsWith('//') && !line.startsWith('/*') && !line.startsWith('*')) {
      break;
    }
  }

  // Insert the logger import
  lines.splice(insertIndex, 0, config.loggerImport);
  stats.importsAdded++;

  return lines.join('\n');
}

/**
 * Replace console.log statements with logger calls
 */
function replaceConsoleLogs(content) {
  let modified = content;
  let replacementCount = 0;

  // Replace console.log statements using the updated pattern
  modified = modified.replace(patterns.consoleLogPattern, (match, indent, args) => {
    // Skip console.error and console.warn (they're allowed)
    if (match.includes('console.error') || match.includes('console.warn')) {
      return match;
    }

    // Skip if already wrapped in development check (simple check)
    const beforeMatch = content.substring(0, content.indexOf(match));
    if (beforeMatch.includes('if (process.env.NODE_ENV === \'development\')') &&
      beforeMatch.lastIndexOf('if (process.env.NODE_ENV === \'development\')') > beforeMatch.lastIndexOf('}')) {
      return match; // Likely already wrapped
    }

    replacementCount++;

    // Clean up args - remove trailing semicolon if present
    args = args.replace(/;$/, '');

    // Determine if this looks like a debug statement or important info
    const isDebugStatement =
      args.includes('[DEBUG]') ||
      args.includes('debug') ||
      args.includes('Debug') ||
      args.toLowerCase().includes('loading') ||
      args.toLowerCase().includes('fetching') ||
      args.toLowerCase().includes('checking') ||
      args.toLowerCase().includes('auth') ||
      args.toLowerCase().includes('user') ||
      args.toLowerCase().includes('event');

    if (isDebugStatement) {
      // Wrap debug statements in development check
      return `${indent}if (process.env.NODE_ENV === 'development') {\n${indent}  logger.debug(${args});\n${indent}}`;
    } else {
      // Important statements use logger.info
      return `${indent}logger.info(${args});`;
    }
  });

  stats.consoleLogsReplaced += replacementCount;
  return { content: modified, replacementCount };
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    stats.filesProcessed++;

    const content = fs.readFileSync(filePath, 'utf8');
    let modified = content;
    let hasChanges = false;

    // Check if file has console.log statements
    const hasConsoleLogs = /console\.log\s*\(/.test(content);

    if (!hasConsoleLogs) {
      return; // No console.log statements to replace
    }

    console.log(`Processing: ${filePath}`);

    // Add logger import if needed
    if (!hasLoggerImport(content)) {
      modified = addLoggerImport(modified);
      hasChanges = true;
    }

    // Replace console.log statements
    const result = replaceConsoleLogs(modified);
    if (result.replacementCount > 0) {
      modified = result.content;
      hasChanges = true;
      console.log(`  → Replaced ${result.replacementCount} console.log statements`);
    }

    // Write changes if not dry run
    if (hasChanges) {
      if (!config.dryRun) {
        fs.writeFileSync(filePath, modified, 'utf8');
        stats.filesModified++;
        console.log(`  ✅ Updated ${filePath}`);
      } else {
        console.log(`  🔍 Would update ${filePath} (dry run)`);
      }
    }

  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    stats.errors++;
  }
}

/**
 * Main execution
 */
function main() {
  console.log('🚀 Starting automated console.log replacement...');
  console.log(`📁 Source directory: ${config.srcDir}`);
  console.log(`🔍 Dry run mode: ${config.dryRun ? 'ON' : 'OFF'}`);
  console.log('');

  // Find all files to process
  const files = findFiles(config.srcDir);
  console.log(`📄 Found ${files.length} files to check`);
  console.log('');

  // Process each file
  for (const file of files) {
    processFile(file);
  }

  // Print summary
  console.log('');
  console.log('📊 SUMMARY:');
  console.log(`   Files processed: ${stats.filesProcessed}`);
  console.log(`   Files modified: ${stats.filesModified}`);
  console.log(`   Console.log statements replaced: ${stats.consoleLogsReplaced}`);
  console.log(`   Logger imports added: ${stats.importsAdded}`);
  console.log(`   Errors: ${stats.errors}`);

  if (config.dryRun) {
    console.log('');
    console.log('🔍 This was a dry run. No files were actually modified.');
    console.log('   Set dryRun: false in the script to apply changes.');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, config, stats };
