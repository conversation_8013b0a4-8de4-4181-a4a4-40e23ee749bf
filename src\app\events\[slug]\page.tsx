import Image from 'next/image';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { Calendar, Clock, MapPin, User, Share2, ExternalLink, Mail, Phone, Users, Shirt, Tag, DollarSign } from 'lucide-react';
import { formatDate } from '@/utils/formatDate';
import EventGallery from '@/components/EventGallery';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { EventRepository } from '@/repositories/event-repository';
import { EventCategoryRepository } from '@/repositories/event-category-repository';
import { cn } from '@/lib/utils';
import { formatCurrency, formatPriceRange, getCurrencyInfo } from '@/lib/utils/currency-utils';
import type { Metadata } from 'next';
import { logger } from '@/lib/logger';

interface EventDetailProps {
  params: Promise<{
    slug: string;
  }>;
}

// Helper function to get price range from event
function getPriceRange(event: any, categories: unknown[] = []) {
  // Use provided categories or fall back to event.eventCategories
  const eventCategories = categories.length > 0 ? categories : (event.eventCategories || []);

  if (eventCategories.length === 0) {
    return null;
  }

  // Get all prices from categories (only from properties.price)
  // Handle both string and number price formats
  const prices = eventCategories
    .map((cat: any) => {
      const price = cat.properties?.price;
      if (price === undefined || price === null) return null;

      // Handle both string and number formats
      return typeof price === 'string' ? parseFloat(price) : price;
    })
    .filter((price: unknown) => price !== null && !isNaN(price)); // Filter out null, undefined, and NaN values

  if (process.env.NODE_ENV === 'development') {
    logger.debug("Extracted prices for range calculation:", prices);
  }

  if (prices.length === 0) {
    return 'Free Entry';
  }

  // Get the country code from the event
  const countryCode = event.country || 'MY';

  // If all prices are the same
  if (prices.every((price: number) => price === prices[0])) {
    return formatCurrency(prices[0], countryCode);
  }

  // Get min and max prices
  const minPrice = Math.min(...prices);
  const maxPrice = Math.max(...prices);

  return formatPriceRange(minPrice, maxPrice, countryCode);
}

// Generate metadata for SEO
export async function generateMetadata({ params }: EventDetailProps): Promise<Metadata> {
  // Await params to fix the "params should be awaited" error in Next.js 14+
  const { slug } = await params;

  // Fetch the event from the database
  const eventRepository = new EventRepository();
  const event = await eventRepository.getEventBySlug(slug);

  if (!event) {
    return {
      title: 'Event Not Found',
    };
  }

  return {
    title: event.title,
    description: event.description?.substring(0, 160) || 'Event details',
    openGraph: {
      title: event.title,
      description: event.description?.substring(0, 160) || 'Event details',
      images: event.coverImage ? [{ url: event.coverImage.url }] : [],
    },
  };
}

export default async function EventDetail({ params }: EventDetailProps) {
  // Await params to fix the "params should be awaited" error in Next.js 14+
  const { slug } = await params;

  // Fetch the event from the database
  const eventRepository = new EventRepository();
  const event = await eventRepository.getEventBySlug(slug);

  if (!event || event.status !== 'published') {
    notFound();
  }

  // Format dates for display
  const formattedStartDate = event.startDate ? formatDate(event.startDate) : 'TBA';
  const formattedEndDate = event.endDate ? formatDate(event.endDate) : 'TBA';

  // Fetch event categories separately
  const categoryRepository = new EventCategoryRepository();
  const eventCategories = await categoryRepository.getCategoriesByEventId(event.id);

  if (process.env.NODE_ENV === 'development') {
    logger.debug(`Loaded ${eventCategories.length} categories for event ${event.title}:`,
      eventCategories.map(cat => ({
        id: cat.id,
        name: cat.name,
        price: cat.properties?.price
      }))
    );
  }

  // Get organizer information
  const organizerName = event.organizerName || event.organizer?.name || 'Event Organizer';

  // Get cover image URL
  const coverImageUrl = event.coverImage?.url || '/images/default-event-cover.jpg';

  // Get poster image URL
  const posterImageUrl = event.posterImage?.url || null;

  // Get gallery images
  const galleryImages = event.galleryImages?.map((img: any) => img.url) || [];

  // Get currency info based on country
  const countryCode = event.country || 'MY';
  const currencyInfo = getCurrencyInfo(countryCode);

  if (process.env.NODE_ENV === 'development') {
    logger.debug(`Event country: ${countryCode}, Currency: ${currencyInfo.code} (${currencyInfo.symbol})`);

    // Log categories and their prices for debugging
    logger.debug("Event categories and prices:", eventCategories.map(cat => ({
      name: cat.name,
      price: cat.properties?.price,
      priceType: typeof cat.properties?.price
    })));
  }

  // Get event price range using the fetched categories
  const priceRange = getPriceRange(event, eventCategories);

  return (
    <main className="min-h-screen pb-16">
      {/* Cover banner */}
      <div className=&quot;relative w-full h-[300px] md:h-[400px] overflow-hidden bg-muted">
        <Image
          src={coverImageUrl}
          alt={event.title}
          fill
          priority
          className="object-cover"
          style={
            event.coverImage &&
              typeof event.coverImage === 'object' &&
              'focusPoint' in event.coverImage &&
              event.coverImage.focusPoint &&
              typeof event.coverImage.focusPoint === 'object' &&
              'y' in event.coverImage.focusPoint ?
              {
                objectPosition: `center ${event.coverImage.focusPoint.y}%`
              } :
              {}
          } />

        <div className=&quot;absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/70" />

        <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
          <div className=&quot;container max-w-5xl mx-auto">
            {eventCategories && eventCategories.length > 0 && (
              <Badge variant="secondary" className=&quot;mb-3">{eventCategories[0]?.name || 'General'}</Badge>
            )}
            <h1 className="text-3xl md:text-4xl font-bold mb-2 text-white">{event.title}</h1>
            <div className=&quot;flex flex-wrap gap-4 text-sm">
              <div className="flex items-center gap-1">
                <Calendar className=&quot;h-4 w-4" />
                <span>{formattedStartDate}</span>
                {formattedStartDate !== formattedEndDate && (
                  <span> - {formattedEndDate}</span>
                )}
              </div>
              <div className="flex items-center gap-1">
                <Clock className=&quot;h-4 w-4" />
                <span>{event.timezone || 'Local Time'}</span>
              </div>
              <div className="flex items-center gap-1">
                <MapPin className=&quot;h-4 w-4" />
                <span>
                  {event.location}
                  {event.city && `, ${event.city}`}
                  {event.state && `, ${event.state}`}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <User className=&quot;h-4 w-4" />
                <span>{organizerName}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container max-w-5xl mx-auto px-4 mt-8">
        <div className=&quot;grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="md:col-span-2 space-y-8">
            {/* Event description */}
            <Card>
              <CardHeader>
                <CardTitle>About This Event</CardTitle>
              </CardHeader>
              <CardContent>
                <p className=&quot;text-foreground whitespace-pre-line">{event.description}</p>
              </CardContent>
            </Card>

            {/* Event gallery */}
            {(posterImageUrl || galleryImages.length > 0) && (
              <Card>
                <CardHeader>
                  <CardTitle>Event Gallery</CardTitle>
                </CardHeader>
                <CardContent>
                  <EventGallery
                    posterUrl={posterImageUrl}
                    galleryUrls={galleryImages}
                    className="w-full" />
                </CardContent>
              </Card>
            )}

            {/* Ticket Categories - Highlighted Section */}
            <div className=&quot;bg-gradient-to-r from-indigo-50 to-indigo-100 dark:from-indigo-950/30 dark:to-indigo-900/20 p-1 rounded-lg">
              <Card className="border-2 border-indigo-200 dark:border-indigo-800">
                <CardHeader className=&quot;bg-gradient-to-r from-indigo-100 to-indigo-50 dark:from-indigo-900/50 dark:to-indigo-800/30">
                  <CardTitle className="flex items-center gap-2 text-2xl">
                    <Tag className=&quot;h-6 w-6 text-indigo-600 dark:text-indigo-400" />
                    Ticket Categories
                  </CardTitle>
                  <CardDescription className="text-base">
                    Available registration options for this event
                  </CardDescription>
                </CardHeader>
                <CardContent className=&quot;space-y-6 pt-6">
                  {eventCategories.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <p>No ticket categories available for this event yet.</p>
                    </div>
                  ) : (
                    eventCategories.map((category: unknown) => (
                      <div key={category.id} className=&quot;border border-border rounded-lg p-5 bg-card shadow-sm">
                        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-3">
                          <h3 className=&quot;font-semibold text-card-foreground text-xl">{category.name}</h3>
                          {category.properties?.price ? (
                            <Badge variant="outline" className=&quot;text-base px-4 py-1.5 bg-indigo-50 text-indigo-700 border-indigo-200 dark:bg-indigo-950 dark:text-indigo-300 dark:border-indigo-800">
                              {formatCurrency(parseFloat(category.properties.price), event.country)}
                            </Badge>
                          ) : (
                            <Badge variant="outline" className=& quot;text-base px-4 py-1.5 bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800&quot;>
                          Free Entry
                        </Badge>
                          )}
                      </div>

                        {
                        category.description && (
                          <p className="text-muted-foreground mb-4">{category.description}</p>
                        )
                      }

                      < div className = "grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm mb-4" >
                      {
                        category.properties?.startTime && (
                          <div className="flex items-center gap-2 bg-muted/50 p-2 rounded">
                            <Clock className=&quot;h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                            <span>Start Time: <span className="font-medium">{new Date(category.properties.startTime).toLocaleTimeString()}</span></span>
                          </div>
                        )
                      }

                          {
                        category.properties?.registrationLimit && (
                          <div className=&quot;flex items-center gap-2 bg-muted/50 p-2 rounded">
                            <Users className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                            <span>Capacity: <span className=&quot;font-medium">{category.properties.registrationCount || 0} / {category.properties.registrationLimit}</span></span>
                          </div>
                        )
                      }

                          {
                        category.properties?.registrationCloseDate && (
                          <div className="flex items-center gap-2 bg-muted/50 p-2 rounded">
                            <Calendar className=&quot;h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                            <span>Registration Closes: <span className="font-medium">{formatDate(category.properties.registrationCloseDate)}</span></span>
                          </div>
                        )
                      }

                          {
                        category.properties?.earlyBirdPrice && (
                          <div className=&quot;flex items-center gap-2 bg-muted/50 p-2 rounded">
                            <DollarSign className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                            <span>Early Bird: <span className=&quot;font-medium">{formatCurrency(parseFloat(category.properties.earlyBirdPrice), event.country)}</span></span>
                          </div>
                        )
                      }
                        </div>

                <div className="mt-4 pt-4 border-t border-border flex justify-end">
                  <Button size=&quot;lg" className="bg-indigo-600 hover:bg-indigo-700 text-white" asChild>
                    <Link href={`/events/${slug}/register/${category.id}`}>
                      Register for {category.name}
                    </Link>
                  </Button>
                </div>
            </div>
            ))
                  )}
          </CardContent>
        </Card>
      </div>

      {/* T-shirt information - moved to bottom */}
      {event.tshirtOptions?.enabled && (
        <Card className=&quot;w-full">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-xl">
              <Shirt className=&quot;h-5 w-5 text-indigo-600 dark:text-indigo-400" />
              T-Shirt Information
            </CardTitle>
            <CardDescription>
              Size information and chart for event T-shirts
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {event.tshirtOptions.description && (
              <div>
                <h3 className=&quot;font-medium text-lg mb-2">Description</h3>
                <p className="text-muted-foreground">{event.tshirtOptions.description}</p>
              </div>
            )}

            {event.tshirtOptions.sizes && event.tshirtOptions.sizes.length > 0 && (
              <div>
                <h3 className=&quot;font-medium text-lg mb-3">Available Sizes</h3>
                <div className="flex flex-wrap gap-2">
                  {event.tshirtOptions.sizes.map((size: string, index: number) => (
                    <Badge key={index} variant=&quot;outline" className="bg-muted px-3 py-1 text-base">
                      {size}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {event.tshirtOptions.sizeChartImage && (
              <div className=&quot;w-full">
                <h3 className="font-medium text-lg mb-3">Size Chart</h3>
                <div className=&quot;relative w-full rounded-md overflow-hidden border border-border">
                  <a
                    href={event.tshirtOptions.sizeChartImage.url}
                    target="_blank"
                    rel=&quot;noopener noreferrer"
                    className="block cursor-zoom-in"
                  >
                    <div className=&quot;relative w-full h-auto aspect-auto min-h-[600px] md:min-h-[700px]">
                      <Image
                        src={event.tshirtOptions.sizeChartImage.url}
                        alt="T-shirt Size Chart"
                        fill
                        className=&quot;object-contain w-full h-full"
                        sizes="100vw"
                        priority
                        style={{
                          maxWidth: & apos; 100% &quot;, maxHeight: '100%' }}
                              unoptimized={ true} />
                          <div className="absolute inset-0 bg-black/5 hover:bg-black/10 transition-colors flex items-center justify-center opacity-0 hover:opacity-100">
                            <div className="bg-black/60 text-white px-4 py-2 rounded-full flex items-center">
                              <svg xmlns=&quot;http://www.w3.org/2000/svg" width="20" height=&quot;20" viewBox="0 0 24 24" fill=&quot;none" stroke="currentColor" strokeWidth=&quot;2" strokeLinecap="round" strokeLinejoin="round" className=" mr-2"><path d="m15 3 6 6m0 0-6 6m6-6H8a5 5 0 0 0 0 10h5"></path></svg>
                            View Full Size
                          </div>
                            </div>
                </div>
              </a>
                      </div>
          <p className="text-xs text-muted-foreground mt-2 text-center">
          Click or tap on the image to view full size
        </p>
                    </div>
  )
}
                </CardContent>
              </Card>
            )}
          </div>

  <div className="space-y-6">
    {/* Price & ticket info */}
    <Card className="bg-card">
      <CardContent className=&quot;p-6">
        <h3 className="text-xl font-semibold mb-3 text-card-foreground">
          {priceRange ? priceRange : 'Free Entry'}
        </h3>

        <Button className=&quot;w-full" asChild>
          <Link href={`/events/${slug}/register`}>
            <ExternalLink className="h-4 w-4 mr-2" />
            Register Now
          </Link>
        </Button>

        <div className=&quot;mt-4 flex justify-between">
          <Button variant="outline" size=&quot;sm" />
          <Share2 className="h-4 w-4 mr-2" />
          Share
        </Button>
        <Button variant=&quot;outline" size="sm" />
        Add to Calendar
      </Button>
  </div>
              </CardContent>
            </Card>

  {/* Registration info */ }
  < Card >
              <CardHeader className=&quot;pb-2">
                <CardTitle className="text-lg">Registration</CardTitle>
              </CardHeader>
              <CardContent>
                {event.registrationCloseDate ? (
                  <div className=&quot;flex items-center gap-2 text-sm mb-3">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className=&quot;text-muted-foreground">Registration closes on:</p>
                      <p className="font-medium">{formatDate(event.registrationCloseDate)}</p>
                    </div>
                  </div>
                ) : (
                  <p className=&quot;text-sm mb-3 text-muted-foreground">Registration is currently open</p>
                )}

                {event.totalCapacity && (
                  <div className="flex items-center gap-2 text-sm">
                    <Users className=&quot;h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-muted-foreground">Event capacity:</p>
                      <p className=&quot;font-medium">{event.totalCapacity} attendees</p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

  {/* Organizer info */ }
  < Card >
              <CardHeader className="pb-2">
                <CardTitle className=&quot;text-lg">Organizer</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="font-medium mb-3">{organizerName}</p>

                <Separator className=&quot;my-3" />

                <div className="space-y-2">
                  {(event.contactEmail || event.organizer?.email) && (
                    <div className=&quot;flex items-center gap-2 text-sm">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <a
                        href={`mailto:${event.contactEmail || event.organizer?.email}`}
                        className=&quot;text-primary hover:underline"
                      >
                        {event.contactEmail || event.organizer?.email}
                      </a>
                    </div>
                  )}

                  {(event.contactPhone || event.organizer?.phone) && (
                    <div className="flex items-center gap-2 text-sm">
                      <Phone className=&quot;h-4 w-4 text-muted-foreground" />
                      <a
                        href={`tel:${event.contactPhone || event.organizer?.phone}`}
                        className="text-primary hover:underline"
                      >
                        {event.contactPhone || event.organizer?.phone}
                      </a>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

  {/* Location info */ }
  < Card >
              <CardHeader className=&quot;pb-2">
                <CardTitle className="text-lg">Location</CardTitle>
              </CardHeader>
              <CardContent>
                <p className=&quot;mb-2">{event.location}</p>
                <p className="text-muted-foreground">
                  {event.city && `${event.city}, `}
                  {event.state}
                  {event.country && `, ${event.country}`}
                </p>

                {event.country && (
                  <div className=&quot;mt-2 text-xs flex items-center gap-1 text-muted-foreground">
                    <DollarSign className="h-3 w-3" />
                    <span>Currency: {currencyInfo.code} ({currencyInfo.symbol})</span>
                  </div>
                )}

                <Button variant=&quot;outline" className="w-full mt-4" asChild/>
                  <Link
                    href={`https://maps.google.com/?q=${encodeURIComponent(
                      `${event.location}, ${event.city || ''} ${event.state}, ${event.country || 'Malaysia'}`
                    )}`}
                    target=&quot;_blank"
                    rel=&quot;noopener noreferrer"
                  >
                    <MapPin className="h-4 w-4 mr-2" />
                    View on Map
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </main>
  );
}