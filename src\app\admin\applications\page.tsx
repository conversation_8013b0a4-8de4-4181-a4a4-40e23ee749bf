"use client";

import { useEffect, useState } from "react";
import { getApplications, approveApplication, rejectApplication } from "../../dashboard/organizations/apply/actions";
import { OrganizationApplication } from "../../../types/roles";

export default function ApplicationsPage() {
  const [applications, setApplications] = useState<OrganizationApplication[]>([]);
  const [loading, setLoading] = useState(true);
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null);
  const [selectedApp, setSelectedApp] = useState<OrganizationApplication | null>(null);
  const [rejectionReason, setRejectionReason] = useState("");
  const [showRejectModal, setShowRejectModal] = useState(false);

  // Load applications
  useEffect(() => {
    loadApplications();
  }, []);

  const loadApplications = async () => {
    try {
      const result = await getApplications({
        status: 'all',
        page: 1,
        limit: 10,
        sortBy: 'updated_at',
        sortDirection: 'desc'
      });
      setApplications(result.applications);
    } catch (error) {
      console.error("Error loading applications:", error);
      setMessage({ type: 'error', text: 'Failed to load applications' });
    } finally {
      setLoading(false);
    }
  };

  // Handle application approval
  const handleApprove = async (applicationId: string) => {
    try {
      const result = await approveApplication(applicationId);
      if (result.success) {
        setMessage({ type: 'success', text: result.message });
        loadApplications(); // Refresh the list
      } else {
        setMessage({ type: 'error', text: result.message });
      }
    } catch (error) {
      console.error("Error approving application:", error);
      setMessage({ type: 'error', text: 'Failed to approve application' });
    }
  };

  // Handle application rejection
  const handleReject = async () => {
    if (!selectedApp || !rejectionReason) return;

    try {
      const result = await rejectApplication(selectedApp.id, rejectionReason);
      if (result.success) {
        setMessage({ type: 'success', text: result.message });
        setShowRejectModal(false);
        setRejectionReason("");
        setSelectedApp(null);
        loadApplications(); // Refresh the list
      } else {
        setMessage({ type: 'error', text: result.message });
      }
    } catch (error) {
      console.error("Error rejecting application:", error);
      setMessage({ type: 'error', text: 'Failed to reject application' });
    }
  };

  return (
    <div className="container mx-auto px-4">
      <h1 className="text-2xl font-semibold mb-6">Organization Applications</h1>

      {message && (
        <div className={`p-4 mb-6 rounded-md ${message.type === 'success' ? 'bg-success/10 text-success' : 'bg-destructive/10 text-destructive'
          }`}>
          {message.text}
        </div>
      )}

      {loading ? (
        <div className="text-center py-8">Loading applications...</div>
      ) : applications.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">No applications to review.</div>
      ) : (
        <div className="bg-card rounded-lg shadow border border-border overflow-hidden">
          <table className="min-w-full divide-y divide-border">
            <thead className="bg-muted">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  Organization
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  Contact
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  Submitted
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-border">
              {applications.map((app) => (
                <tr key={app.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="font-medium text-foreground">{app.data.organizationName}</div>
                    <div className="text-sm text-muted-foreground">{app.data.organizationType}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-foreground">{app.data.contactName}</div>
                    <div className="text-sm text-muted-foreground">{app.data.contactEmail}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${app.status === 'submitted' ? 'bg-warning/20 text-warning' :
                      app.status === 'approved' ? 'bg-success/20 text-success' :
                        app.status === 'rejected' ? 'bg-destructive/20 text-destructive' :
                          'bg-muted text-muted-foreground'
                      }`}>
                      {app.status.charAt(0).toUpperCase() + app.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-muted-foreground">
                    {app.submittedAt ? new Date(app.submittedAt).toLocaleDateString() : 'Not submitted'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    {app.status === 'submitted' && (
                      <div className="space-x-2">
                        <button
                          onClick={() => handleApprove(app.id)}
                          className="text-success hover:text-success/80"
                        >
                          Approve
                        </button>
                        <button
                          onClick={() => {
                            setSelectedApp(app);
                            setShowRejectModal(true);
                          }}
                          className="text-destructive hover:text-destructive/80"
                        >
                          Reject
                        </button>
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {/* Rejection Modal */}
      {showRejectModal && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-card text-card-foreground rounded-lg p-6 max-w-lg w-full border border-border shadow-lg">
            <h3 className="text-lg font-medium mb-4">Reject Application</h3>
            <textarea
              value={rejectionReason}
              onChange={(e) => setRejectionReason(e.target.value)}
              placeholder="Please provide a reason for rejection...&quot;
              className="w-full px-3 py-2 bg-background border border-input rounded-md mb-4"
              rows={4}
            />
            <div className=&quot;flex justify-end space-x-2">
              <button
                onClick={() => {
                  setShowRejectModal(false);
                  setRejectionReason("");
                  setSelectedApp(null);
                }}
                className="px-4 py-2 border border-input rounded-md hover:bg-accent"
              >
                Cancel
              </button>
              <button
                onClick={handleReject}
                disabled={!rejectionReason.trim()}
                className="px-4 py-2 bg-destructive text-destructive-foreground rounded-md hover:bg-destructive/90 disabled:opacity-50"
              >
                Reject Application
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}