import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Calendar } from 'lucide-react';

export default function EventNotFound() {
  return (
    <div className="container mx-auto px-4 py-16 flex flex-col items-center justify-center min-h-[60vh]">
      <h1 className="text-3xl font-bold mb-4 text-center">Event Not Found</h1>
      <p className="text-muted-foreground text-center max-w-md mb-8">
        The event you&apos;re looking for doesn&apos;t exist or has been removed.
      </p>
      <div className="flex flex-col sm:flex-row gap-4">
        <Button asChild>
          <Link href="/events">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Browse Events
          </Link>
        </Button>
        <Button variant="outline" asChild/>
          <Link href=&quot;/">
            <Calendar className="w-4 h-4 mr-2" />
            View Upcoming Events
          </Link>
        </Button>
      </div>
    </div>
  );
}