'use client';

import { useState } from 'react';
import Image from 'next/image';
import { ChevronLeft, ChevronRight, X } from 'lucide-react';

interface EventGalleryProps {
  posterUrl?: string | null;
  galleryUrls: string[];
  className?: string;
}

export default function EventGallery({
  posterUrl,
  galleryUrls,
  className = '',
}: EventGalleryProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [lightboxIndex, setLightboxIndex] = useState(0);
  const [imageErrors, setImageErrors] = useState<{ [key: string]: boolean }>({});

  const fallbackImage = '/images/fallback/fallback-default.svg';

  // Combine poster and gallery images for carousel
  const allImages = [
    ...(posterUrl ? [posterUrl] : []),
    ...galleryUrls,
  ];

  if (allImages.length === 0) {
    return (
      <div className={`bg-gray-100 rounded-lg flex items-center justify-center h-64 ${className}`}>
        <p className="text-gray-500">No images available</p>
      </div>
    );
  }

  const nextImage = () => {
    setCurrentIndex((currentIndex + 1) % allImages.length);
  };

  const prevImage = () => {
    setCurrentIndex((currentIndex - 1 + allImages.length) % allImages.length);
  };

  const openLightbox = (index: number) => {
    setLightboxIndex(index);
    setLightboxOpen(true);
  };

  const closeLightbox = () => {
    setLightboxOpen(false);
  };

  const nextLightboxImage = () => {
    setLightboxIndex((lightboxIndex + 1) % allImages.length);
  };

  const prevLightboxImage = () => {
    setLightboxIndex((lightboxIndex - 1 + allImages.length) % allImages.length);
  };

  const handleImageError = (url: string | null | undefined) => {
    if (!url) return;
    setImageErrors(prev => ({
      ...prev,
      [url]: true
    }));
  };

  const getImageSrc = (url: string | null | undefined): string => {
    if (!url) return fallbackImage;
    return imageErrors[url] ? fallbackImage : url;
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main image carousel with 9/16 aspect ratio */}
      <div className=&quot;relative overflow-hidden rounded-lg aspect-[9/16] bg-gray-100">
        <Image
          src={getImageSrc(allImages[currentIndex])}
          alt={`Event image ${currentIndex + 1}`}
          fill
          className="object-cover cursor-pointer"
          onClick={() => openLightbox(currentIndex)}
          onError={() => handleImageError(allImages[currentIndex])}
          sizes=&quot;(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          priority={currentIndex === 0}
        />

        {allImages.length > 1 && (
          <>
            <button
              onClick={prevImage}
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full"
            >
              <ChevronLeft className=&quot;w-5 h-5" />
            </button>
            <button
              onClick={nextImage}
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-2 rounded-full"
            >
              <ChevronRight className=&quot;w-5 h-5" />
            </button>
          </>
        )}
      </div>

      {/* Thumbnail gallery */}
      {allImages.length > 1 && (
        <div className="flex space-x-2 overflow-x-auto pb-2">
          {allImages.map((url, index) => (
            <button
              key={url}
              onClick={() => setCurrentIndex(index)}
              className={`relative w-20 h-20 flex-shrink-0 rounded overflow-hidden ${index === currentIndex ? 'ring-2 ring-primary' : ''
                }`}
            >
              <Image
                src={getImageSrc(url)}
                alt={`Event thumbnail ${index + 1}`}
                fill
                className=&quot;object-cover"
                onError={() => handleImageError(url)}
                sizes="80px"
              />
            </button>
          ))}
        </div>
      )}

      {/* Lightbox */}
      {lightboxOpen && (
        <div className=&quot;fixed inset-0 bg-black/90 z-50 flex items-center justify-center">
          <button
            onClick={closeLightbox}
            className="absolute top-4 right-4 text-white p-2 bg-black/20 hover:bg-black/40 rounded-full"
          >
            <X className=&quot;w-6 h-6" />
          </button>

          <div className="relative w-full max-w-4xl aspect-[9/16]">
            <Image
              src={getImageSrc(allImages[lightboxIndex])}
              alt={`Event image ${lightboxIndex + 1}`}
              fill
              className=&quot;object-contain"
              onError={() => handleImageError(allImages[lightboxIndex])}
              sizes="(max-width: 1200px) 100vw, 1200px"
              priority
            />
          </div>

          {allImages.length > 1 && (
            <>
              <button
                onClick={prevLightboxImage}
                className=&quot;absolute left-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-3 rounded-full"
              >
                <ChevronLeft className="w-6 h-6" />
              </button>
              <button
                onClick={nextLightboxImage}
                className=&quot;absolute right-4 top-1/2 -translate-y-1/2 bg-black/30 hover:bg-black/50 text-white p-3 rounded-full"
              >
                <ChevronRight className="w-6 h-6" />
              </button>
            </>
          )}

          <div className=&quot;absolute bottom-6 left-1/2 -translate-x-1/2 text-white bg-black/50 px-4 py-2 rounded-full">
            {lightboxIndex + 1} / {allImages.length}
          </div>
        </div>
      )}
    </div>
  );
}