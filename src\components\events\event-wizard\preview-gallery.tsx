'use client';

import { useState } from 'react';
import Image from 'next/image';
import { ChevronLeft, ChevronRight } from 'lucide-react';

interface PreviewGalleryProps {
  posterImage?: { url: string; path: string } | null;
  className?: string;
}

export function PreviewGallery({
  posterImage,
  className = '',
}: PreviewGalleryProps) {
  const fallbackImage = '/images/fallback/fallback-default.svg';
  const [imageError, setImageError] = useState(false);

  if (!posterImage) {
    return (
      <div className={`bg-[hsl(var(--muted))] rounded-lg flex items-center justify-center h-64 ${className}`}>
        <p className="text-[hsl(var(--muted-foreground))]">No images available</p>
      </div>
    );
  }

  const handleImageError = () => {
    setImageError(true);
  };

  const imageSrc = imageError ? fallbackImage : posterImage.url;

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main image display - full width */}
      <div className=&quot;relative overflow-hidden rounded-lg bg-[hsl(var(--muted))]">
        <div className="relative w-full aspect-[9/16]">
          <Image
            src={imageSrc}
            alt="Event poster"
            fill
            className=&quot;object-contain"
            onError={handleImageError}
            sizes="100vw"
            priority />
        </div>
      </div>
    </div>
  );
}
