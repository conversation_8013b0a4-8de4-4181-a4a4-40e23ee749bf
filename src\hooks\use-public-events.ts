import { useState, useEffect } from 'react';
import { Event } from '@/repositories/event-repository';

/**
 * Hook for fetching public events
 */
export function usePublicEvents(category?: string, state?: string) {
  const [events, setEvents] = useState<Event[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchEvents = async () => {
      try {
        setLoading(true);
        
        // Build the query string
        const url = '/api/public/events';
        const params = new URLSearchParams();
        
        if (category) {
          params.append('category', category);
        }
        
        if (state) {
          params.append('state', state);
        }
        
        if (params.toString()) {
          url += `?${params.toString()}`;
        }
        
        const response = await fetch(url);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch events: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        setEvents(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch events'));
        setEvents([]);
      } finally {
        setLoading(false);
      }
    };

    fetchEvents();
  }, [category, state]);

  return { events, loading, error };
}

/**
 * Hook for fetching a single public event by slug
 */
export function usePublicEventBySlug(slug: string) {
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchEvent = async () => {
      try {
        setLoading(true);
        
        const response = await fetch(`/api/public/events?slug=${encodeURIComponent(slug)}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch event: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        setEvent(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch event'));
        setEvent(null);
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchEvent();
    }
  }, [slug]);

  return { event, loading, error };
}

/**
 * Hook for fetching a single public event by ID
 */
export function usePublicEventById(id: string) {
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchEvent = async () => {
      try {
        setLoading(true);
        
        const response = await fetch(`/api/public/events?id=${encodeURIComponent(id)}`);
        
        if (!response.ok) {
          throw new Error(`Failed to fetch event: ${response.status} ${response.statusText}`);
        }
        
        const data = await response.json();
        setEvent(data);
        setError(null);
      } catch (err) {
        setError(err instanceof Error ? err : new Error('Failed to fetch event'));
        setEvent(null);
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchEvent();
    }
  }, [id]);

  return { event, loading, error };
}
