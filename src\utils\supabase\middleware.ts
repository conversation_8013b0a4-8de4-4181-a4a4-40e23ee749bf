import { createServer<PERSON><PERSON>, type CookieOptions } from '@supabase/ssr'
import { NextResponse, type NextRequest } from 'next/server'
import { logger } from '@/lib/logger';

/**
 * Update the session in the middleware
 * This function should be called at the beginning of the middleware function
 * to ensure the session is refreshed and cookies are properly handled
 *
 * @param request The Next.js request object
 * @returns A Next.js response object with updated cookies
 */
export async function updateSession(request: NextRequest) {
  // Create a response object that we can modify
  let supabaseResponse = NextResponse.next({
    request: {
      headers: request.headers,
    },
  })

  // Get the redirect count from headers or initialize it
  const redirectCount = parseInt(request.headers.get('x-redirect-count') || '0');

  // Check for middleware prefetch (Next.js internal)
  const isMiddlewarePrefetch = request.headers.get('x-middleware-prefetch') === '1';

  // If this is a middleware prefetch, mark it as a background refresh
  if (isMiddlewarePrefetch) {
    request.headers.set('x-background-refresh', 'true');
  }

  // Track the redirect count in the headers
  request.headers.set('x-redirect-count', redirectCount.toString());

  // Check for reset-complete cookie to handle auth reset
  const resetComplete = request.cookies.get('sb-reset-complete');
  if (resetComplete) {
    request.headers.set('x-auth-reset-complete', 'true');
  }

  // Log cookies for debugging
  if (process.env.NODE_ENV === 'development') {
    const cookieNames = request.cookies.getAll().map(c => c.name);
    logger.info('Middleware cookies before client creation:', cookieNames);
  }

  // Create a Supabase client for the middleware
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return request.cookies.getAll()
        },
        setAll(cookiesToSet) {
          // Log cookie operations in development
          if (process.env.NODE_ENV === 'development' && cookiesToSet.length > 0) {
            logger.info('Middleware setting cookies:', cookiesToSet.map(c => c.name));
          }

          // First set cookies on the request
          for (const { name, value, options } of cookiesToSet) {
            request.cookies.set(name, value)
          }

          // Create a new response with the updated request
          supabaseResponse = NextResponse.next({
            request: {
              headers: request.headers,
            },
          })

          // Then set cookies on the response
          for (const { name, value, options } of cookiesToSet) {
            supabaseResponse.cookies.set(name, value, options)
          }
        },
      },
    }
  )

  // Do not run code between createServerClient and
  // supabase.auth.getUser(). A simple mistake could make it very hard to debug
  // issues with users being randomly logged out.

  // IMPORTANT: DO NOT REMOVE auth.getUser() and auth.getSession()
  // This will refresh the user's session and get a new access token if needed

  try {
    // Check if this is a background refresh request
    const isBackgroundRefresh =
      request.headers.get('x-background-refresh') === 'true' ||
      request.headers.get('x-middleware-prefetch') === '1' ||
      redirectCount > 2;

    // Add a header to track background refreshes
    if (isBackgroundRefresh) {
      request.headers.set('x-background-refresh', 'true');
      supabaseResponse.headers.set('x-background-refresh', 'true');
    }

    // Always use getUser() first for security - it validates the JWT
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    // Only refresh the session if this is not a background refresh
    // This prevents unnecessary session refreshes that can cause redirect loops
    if (!isBackgroundRefresh && user && !userError) {
      // Get the session to ensure it's refreshed
      await supabase.auth.getSession();
    }

    // Log session state in development
    if (process.env.NODE_ENV === 'development') {
      console.log('Middleware updateSession:', {
        hasUser: !!user,
        userError?.message,
        url: request.url,
        isBackgroundRefresh
      });
    }

    // If we have the reset-complete cookie, add it to the response
    // This ensures it's properly passed through to the client
    if (resetComplete) {
      supabaseResponse.cookies.set('sb-reset-complete', resetComplete.value, {
        maxAge: 60,
        path: '/',
      });
    }
  } catch (error) {
    console.error('Error in updateSession:', error);

    // If there's an error, we still want to return a valid response
    // This prevents the middleware from crashing
    return supabaseResponse;
  }

  // IMPORTANT: You *must* return the supabaseResponse object as it is.
  // If you're creating a new response object with NextResponse.next() make sure to:
  // 1. Pass the request in it, like so:
  //    const myNewResponse = NextResponse.next({ request })
  // 2. Copy over the cookies, like so:
  //    myNewResponse.cookies.setAll(supabaseResponse.cookies.getAll())
  // 3. Change the myNewResponse object to fit your needs, but avoid changing
  //    the cookies!
  // 4. Finally:
  //    return myNewResponse
  // If this is not done, you may be causing the browser and server to go out
  // of sync and terminate the user's session prematurely!

  return supabaseResponse
}
