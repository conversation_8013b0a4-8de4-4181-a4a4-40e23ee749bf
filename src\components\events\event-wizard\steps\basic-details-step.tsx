'use client';

import { useState, useEffect, useCallback } from 'react';
import { useWizard } from '@/components/events/event-wizard/wizard-container';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { z } from 'zod';
import { toast } from '@/components/ui/use-toast';
import { getCountries, getStates, FALLBACK_COUNTRIES, FALLBACK_STATES, Country, State } from '@/data/countries';
import { SearchableSelect, Option } from '@/components/ui/searchable-select';
import { toInputDateTime } from '@/lib/utils/date-utils';
import { Switch } from '@/components/ui/switch';
import { cn } from '@/lib/utils';
import { logger } from '@/lib/logger';

// Validation schema
const basicDetailsSchema = z.object({
  title: z.string().min(3, 'Title must be at least 3 characters'),
  description: z.string().min(10, 'Description must be at least 10 characters'),
  location: z.string().min(3, 'Location must be at least 3 characters'),
  country: z.string().min(2, 'Please select a country'),
  state: z.string().min(1, 'Please select a state'),
  city: z.string().min(2, 'City is required').optional(),
  startDate: z.string().min(1, 'Start date is required'),
  endDate: z.string().optional(),
  totalCapacity: z.union([
    z.string().transform(val => val === '' ? undefined : parseInt(val, 10)),
    z.number().int().positive('Capacity must be a positive number'),
    z.undefined()
  ]).optional(),
  registrationCloseDate: z.string().optional(),
  allowCategorySpecificClosingDates: z.boolean().optional().default(false),
  emergencyContactSettings: z.object({
    required: z.boolean().optional().default(false),
    fields: z.array(z.string()).optional().default(['name', 'phone', 'relationship']),
    allowSameForMultipleRegistrations: z.boolean().optional().default(true),
  }).optional().default({
    required: false,
    fields: ['name', 'phone', 'relationship'],
    allowSameForMultipleRegistrations: true,
  }),
});

export function BasicDetailsStep() {
  const { formData, updateFormData, nextStep } = useWizard();
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [countryOptions, setCountryOptions] = useState<Option[]>([]);
  const [stateOptions, setStateOptions] = useState<Option[]>([]);
  const [isLoadingCountries, setIsLoadingCountries] = useState(true);
  const [isLoadingStates, setIsLoadingStates] = useState(false);

  // Add autofocus to first input when component mounts
  useEffect(() => {
    const titleInput = document.querySelector('input[name="title"]&apos;) as HTMLInputElement;
    if (titleInput) {
      titleInput.focus();
    }
  }, []);

  // Fetch countries on component mount
  useEffect(() => {
    const fetchCountryOptions = async () => {
      setIsLoadingCountries(true);
      try {
        // Try to fetch countries but immediately fall back if any error occurs
        let countries: Country[] = [];
        try {
          countries = await getCountries();
        } catch {
          countries = FALLBACK_COUNTRIES;
        }

        // Map the countries to options - using icon for flag to avoid duplication
        const options = countries.map((country: Country) => ({
          value: country.code,
          label: country.name,
          icon: <span className="mr-1">{country.flag}</span>
        }));

        setCountryOptions(options);
      } catch (error) {
        // Ultimate fallback - use FALLBACK_COUNTRIES directly
        const options = FALLBACK_COUNTRIES.map((country: Country) => ({
          value: country.code,
          label: country.name,
          icon: <span className="mr-1">{country.flag}</span>
        }));
        setCountryOptions(options);
        console.warn('Using fallback country options due to error');
      } finally {
        setIsLoadingCountries(false);
      }
    };

    fetchCountryOptions();
  }, []);

  // Handle country change and fetch states
  const handleCountryChange = useCallback(async (countryCode: string) => {
    if (!countryCode) {
      setStateOptions([]);
      updateFormData({ state: '' });
      return;
    }

    setIsLoadingStates(true);
    try {
      // Try to fetch states but immediately fall back if any error occurs
      let states: State[] = [];
      try {
        states = await getStates(countryCode);
      } catch {
        states = FALLBACK_STATES[countryCode] || [];
      }

      const options = states.map((state: State) => ({
        value: state.code,
        label: state.name
      }));

      setStateOptions(options);
    } catch (error) {
      // Ultimate fallback - use FALLBACK_STATES directly
      const fallbackStates = FALLBACK_STATES[countryCode] || [];
      const options = fallbackStates.map((state: State) => ({
        value: state.code,
        label: state.name
      }));
      setStateOptions(options);
      console.warn(`Using fallback states for ${countryCode} due to error`);
    } finally {
      setIsLoadingStates(false);
    }
  }, [updateFormData]);

  // Fetch states when country changes
  useEffect(() => {
    if (formData.country) {
      handleCountryChange(formData.country);
    }
  }, [formData.country, handleCountryChange]);

  useEffect(() => {
    // Debug log for form data changes
    logger.info('Form data updated:', formData);
  }, [formData]);

  // Log available country options
  useEffect(() => {
    logger.info('Available country options:', countryOptions.length);
  }, [countryOptions]);

  // Helper function to quickly jump to a country by first letter
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only process if not in a text input or textarea
      if (
        document.activeElement instanceof HTMLInputElement ||
        document.activeElement instanceof HTMLTextAreaElement
      ) {
        return;
      }

      // Check if it's a letter key
      if (/^[a-z]$/i.test(e.key) && countryOptions.length > 0) {
        // Find first country starting with this letter
        const letter = e.key.toLowerCase();
        const matchingCountry = countryOptions.find(option =>
          option.label.toLowerCase().startsWith(letter)
        );

        if (matchingCountry) {
          // Open the country dropdown and focus
          const countryButton = document.querySelector('[aria-label="Select country"]&apos;) as HTMLButtonElement;
          if (countryButton) {
            countryButton.click();
            setTimeout(() => {
              const searchInput = document.querySelector(&quot;[cmdk-input]') as HTMLInputElement;
              if (searchInput) {
                searchInput.value = letter;
                searchInput.dispatchEvent(new Event('input', { bubbles: true }));
                searchInput.focus();
              }
            }, 100);
          }
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [countryOptions]);

  // Format dates for input fields if they exist in the form data
  useEffect(() => {
    // Check if dates exist but aren't in the right format for the input
    if (formData.startDate && typeof formData.startDate === 'object') {
      updateFormData({
        startDate: toInputDateTime(formData.startDate)
      });
    }

    if (formData.endDate && typeof formData.endDate === 'object') {
      updateFormData({
        endDate: toInputDateTime(formData.endDate)
      });
    }
  }, []);

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    updateFormData({ [name]: value });
    // Clear validation error when field is edited
    if (validationErrors[name]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleCountrySelect = (value: string) => {
    // Update form data directly
    updateFormData({
      country: value,
      state: '' // Reset state when country changes
    });

    // Clear any validation errors
    if (validationErrors.country) {
      const newErrors = { ...validationErrors };
      delete newErrors.country;
      setValidationErrors(newErrors);
    }
  };

  // State selection handler
  const handleStateSelect = (value: string) => {
    // Update form data directly
    updateFormData({
      state: value
    });

    // Clear any validation errors
    if (validationErrors.state) {
      const newErrors = { ...validationErrors };
      delete newErrors.state;
      setValidationErrors(newErrors);
    }
  };

  const validateAndProceed = () => {
    try {
      basicDetailsSchema.parse(formData);
      nextStep();
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors: Record<string, string> = {};
        error.errors.forEach(err => {
          if (err.path.length > 0) {
            if (err.path[0]) {
              errors[err.path[0]] = err.message;
            }
          }
        });
        setValidationErrors(errors);

        toast({
          title: "Validation Error",
          description: "Please fix the errors before proceeding",
          variant: "destructive",
        });
      }
    }
  };

  // Override next button to include validation
  const originalNextButton = document.getElementById('wizard-next-button') as HTMLButtonElement;
  if (originalNextButton) {
    originalNextButton.onclick = validateAndProceed;
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className=&quot;text-2xl font-semibold mb-2">Basic Event Details</h2>
        <p className="text-[hsl(var(--muted-foreground))]">Provide essential information about your event</p>
      </div>

      <div className=&quot;space-y-6">
        <div className="space-y-2">
          <Label htmlFor=&quot;title">Event Title</Label>
          <Input
            id="title"
            name=&quot;title"
            value={formData.title || ''}
            onChange={handleChange}
            placeholder="Enter a descriptive title for your event"
            className={validationErrors.title ? &quot;border-red-500" : ""}
          //>
          {validationErrors.title && (
            <p className="text-sm text-red-500">{validationErrors.title}</p>
          )}
        </div>

        <div className=&quot;space-y-2">
          <Label htmlFor="description"/>Description</Label>
          <Textarea
            id=&quot;description"
            name="description"
            value={formData.description || &apos;&quot;}
            onChange={handleChange}
            placeholder="Describe your event"
            rows={5}
            className={validationErrors.description ? "border-red-500" : ""} />
          {validationErrors.description && (
            <p className="text-sm text-red-500">{validationErrors.description}</p>
          )}
        </div>

        <div className=&quot;space-y-2">
          <Label htmlFor="location"/>Location</Label>
          <Input
            id=&quot;location"
            name="location"
            value={formData.location || &apos;&quot;}
            onChange={handleChange}
            placeholder=&quot;Event venue or address"
            className={validationErrors.location ? "border-red-500" : ""}
          //>
          {validationErrors.location && (
            <p className="text-sm text-red-500">{validationErrors.location}</p>
          )}
        </div>

        <div className=&quot;grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor=&quot;country">Country</Label>
            <SearchableSelect
              options={countryOptions}
              value={formData.country || ''}
              onValueChange={handleCountrySelect}
              placeholder={isLoadingCountries ? "Loading countries..." : "Select a country"}
              searchPlaceholder="Search countries..."
              noResultsText="No countries found"
              error={!!validationErrors.country}
              className=&quot;country-select"
              aria-label="Select country"
              isLoading={isLoadingCountries}
              disabled={isLoadingCountries} />
            {validationErrors.country && (
              <p className="text-sm text-red-500">{validationErrors.country}</p>
            )}
          </div>

          <div className=&quot;space-y-2">
            <Label htmlFor="state"/>State / Province</Label>
            <SearchableSelect
              options={stateOptions}
              value={formData.state || &apos;&quot;}
              onValueChange={handleStateSelect}
              placeholder={isLoadingStates ? "Loading states..." : !formData.country ? "Select a country first" : "Select a state/province"}
              searchPlaceholder="Search states..."
              noResultsText="No states found"
              disabled={!formData.country || isLoadingStates}
              isLoading={isLoadingStates}
              error={!!validationErrors.state}
              className=&quot;state-select"
              aria-label="Select state" />
            {validationErrors.state && (
              <p className="text-sm text-red-500">{validationErrors.state}</p>
            )}
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="city"/>City</Label>
          <Input
            id=&quot;city"
            name="city"
            value={formData.city || &apos;&quot;}
            onChange={handleChange}
            placeholder="City"
            className={validationErrors.city ? "border-red-500" : ""}
          //>
          {validationErrors.city && (
            <p className="text-sm text-red-500">{validationErrors.city}</p>
          )}
        </div>

        <div className=&quot;grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor=&quot;startDate">Start Date</Label>
            <Input
              id="startDate"
              name=&quot;startDate"
              type="datetime-local"
              value={formData.startDate || &apos;&quot;}
              onChange={handleChange}
              className={validationErrors.startDate ? "border-red-500" : ""}
            //>
            {validationErrors.startDate && (
              <p className="text-sm text-red-500">{validationErrors.startDate}</p>
            )}
          </div>

          <div className=&quot;space-y-2">
            <Label htmlFor="endDate"/>End Date</Label>
            <Input
              id=&quot;endDate"
              name="endDate"
              type="datetime-local"
              value={formData.endDate || ''}
              onChange={handleChange}
              className={validationErrors.endDate ? &quot;border-red-500" : ""}
            //>
            {validationErrors.endDate && (
              <p className="text-sm text-red-500">{validationErrors.endDate}</p>
            )}
          </div>
        </div>

        <div className=&quot;space-y-2">
          <Label htmlFor="totalCapacity"/>Total Event Capacity</Label>
          <Input
            id=&quot;totalCapacity"
            name="totalCapacity"
            type="number"
            min="1"
            value={formData.totalCapacity || &apos;"}
            onChange={handleChange}
            placeholder="Maximum number of participants across all categories"
            className={validationErrors.totalCapacity ? &quot;border-red-500" : ""}
          //>
          {validationErrors.totalCapacity && (
            <p className="text-sm text-red-500">{validationErrors.totalCapacity}</p>
          )}
          <p className=&quot;text-xs text-[hsl(var(--muted-foreground))]">
            The total capacity for the entire event. Individual category limits will be set in the Categories step.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className=&quot;space-y-2">
            <Label htmlFor="registrationCloseDate"/>Registration Closing Date</Label>
            <Input
              id=&quot;registrationCloseDate"
              name="registrationCloseDate"
              type="datetime-local"
              value={formData.registrationCloseDate || ''}
              onChange={handleChange}
              className={validationErrors.registrationCloseDate ? &quot;border-red-500" : ""}
              disabled={formData.allowCategorySpecificClosingDates}
            //>
            {validationErrors.registrationCloseDate && (
              <p className="text-sm text-red-500">{validationErrors.registrationCloseDate}</p>
            )}
          </div>

          <div className=&quot;flex items-center space-x-2 pt-8">
            <input
              type="checkbox"
              id=&quot;allowCategorySpecificClosingDates"
              name="allowCategorySpecificClosingDates"
              checked={formData.allowCategorySpecificClosingDates || false}
              onChange={(e) =/> {
                updateFormData({ allowCategorySpecificClosingDates: e.target.checked });
              }}
              className=&quot;h-4 w-4 rounded border-[hsl(var(--border))] text-[hsl(var(--primary))] focus:ring-[hsl(var(--primary))]"
            />
            <Label htmlFor="allowCategorySpecificClosingDates" className=&quot;font-normal"/>
              Set different closing dates for each category
            </Label>
          </div>
        </div>

        <div className="border-t border-[hsl(var(--border))] pt-6 mt-6">
          <h3 className=&quot;text-lg font-medium mb-4">Emergency Contact Settings</h3>

          <div className="flex items-center space-x-2 mb-4">
            <Switch
              id=&quot;emergencyContactRequired"
              checked={formData.emergencyContactSettings?.required || false}
              onCheckedChange={(checked) => {
                updateFormData({
                  emergencyContactSettings: {
                    ...formData.emergencyContactSettings,
                    required: checked
                  }
                });
              }}
            />
            <Label htmlFor="emergencyContactRequired" className="font-medium"/>
              Require emergency contact information from participants
            </Label>
          </div>

          <p className="text-sm text-muted-foreground ml-10">
            When enabled, participants will be required to provide emergency contact details during registration.
            If they have emergency contacts in their profile, these will be auto-populated.
          </p>
        </div>
      </div>
    </div>
  );
}