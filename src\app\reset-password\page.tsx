import { Metadata } from 'next'
import ResetPasswordForm from './reset-password-form'
import { createClient } from '@/lib/supabase/pages-client'
import { redirect } from 'next/navigation'

export const metadata: Metadata = {
  title: 'Reset Password | Fuiyoo',
  description: 'Reset your Fuiyoo account password',
}

export default async function ResetPasswordPage() {
  // Check if user is already signed in
  const supabase = await createClient()
  const { data: { session } } = await supabase.auth.getSession()

  // If already signed in, redirect to dashboard
  if (session) {
    redirect('/dashboard')
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-12">
      <div className=&quot;w-full max-w-md">
        <div className="bg-card text-card-foreground shadow-lg rounded-lg p-8">
          <ResetPasswordForm />
        </div>
      </div>
    </div>
  )
}
