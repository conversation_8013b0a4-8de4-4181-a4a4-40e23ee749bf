'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { useSearchParams } from 'next/navigation'
import { isEventRegistrationUrl } from '@/utils/url-utilities'
import { useOAuthSignIn } from '@/hooks/use-auth-hooks'
import { logger } from '@/lib/logger';

interface GoogleSignInButtonProps {
  className?: string
  redirectTo?: string
  label?: string
}

export default function GoogleSignInButton({
  className = '',
  redirectTo,
  label = 'Sign in with Google',
}: GoogleSignInButtonProps) {
  const searchParams = useSearchParams()
  const { signIn, loading, error } = useOAuthSignIn()

  let defaultRedirectUrl = searchParams.get('redirect_url') || '/dashboard'

  // Check for no_redirect parameter
  const noRedirect = searchParams.get('no_redirect') === 'true'

  // Prevent redirect loops with auth callback or if no_redirect is set
  if (defaultRedirectUrl.includes('/auth/callback') ||
    defaultRedirectUrl.includes('no_redirect=true') ||
    defaultRedirectUrl.includes('/sign-in') ||
    noRedirect) {
    console.warn('Detected potential auth redirect loop in GoogleSignInButton, redirecting to dashboard with no_redirect=true')
    defaultRedirectUrl = '/dashboard?no_redirect=true'
  }

  // Make sure the redirect URL is properly formatted
  if (defaultRedirectUrl && !defaultRedirectUrl.startsWith('/') && !defaultRedirectUrl.startsWith('http')) {
    defaultRedirectUrl = `/${defaultRedirectUrl}`;
  }

  // Log if this is an event registration URL
  useEffect(() => {
    const finalRedirectUrl = redirectTo || defaultRedirectUrl
    if (isEventRegistrationUrl(finalRedirectUrl)) {
      logger.info('Google sign-in initiated from event registration page:', finalRedirectUrl);
    }

    // Log any errors from the hook
    if (error) {
      console.error('OAuth sign-in error:', error)
    }
  }, [redirectTo, defaultRedirectUrl, error])

  const handleSignIn = async () => {
    // Ensure we're using a clean redirect URL
    const finalRedirectUrl = redirectTo || defaultRedirectUrl

    logger.info('Google sign-in initiated with redirect URL:', finalRedirectUrl);
    // Use our custom hook to handle the sign-in process
    await signIn('google', finalRedirectUrl)
  }

  return (
    <Button
      variant="outline"
      type="button"
      disabled={loading}
      className={`w-full flex items-center justify-center gap-2 text-foreground border-input ${className}`}
      onClick={handleSignIn}
    />
      {loading ? (
        <span className="animate-spin">⟳</span>
      ) : (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 24 24"
          width="24"
          height="24"
          className="w-5 h-5"
        >
          <path
            fill="#4285F4"
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
          />
          <path
            fill="#34A853"
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
          />
          <path
            fill="#FBBC05"
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
          />
          <path
            fill="#EA4335"
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
          />
          <path fill="none" d=&quot;M1 1h22v22H1z" />
        </svg>
      )}
      <span>{loading ? 'Signing in...' : label}</span>
    </Button>
  )
}
