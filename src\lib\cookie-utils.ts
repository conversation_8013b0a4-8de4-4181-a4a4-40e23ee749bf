'use server';

import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

/**
 * Cookie options interface
 */
interface CookieOptions {
  /** Cookie expiration in seconds */
  maxAge?: number;

  /** Cookie path */
  path?: string;

  /** Cookie domain */
  domain?: string;

  /** Whether the cookie is secure */
  secure?: boolean;

  /** Whether the cookie is HTTP only */
  httpOnly?: boolean;

  /** Same-site attribute */
  sameSite?: 'strict' | 'lax' | 'none';
}

/**
 * Default cookie options
 */
const defaultCookieOptions = {
  path: '/',
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax',
};

/**
 * Get all cookies
 * @returns All cookies
 */
export async function getAllCookies() {
  const cookieStore = await cookies();
  return cookieStore.getAll();
}

/**
 * Get a cookie by name
 * @param name Cookie name
 * @returns Cookie value or null if not found
 */
export async function getCookie(name: string) {
  const cookieStore = await cookies();
  const cookie = cookieStore.get(name);
  return cookie?.value || null;
}

/**
 * Set a cookie
 * @param name Cookie name
 * @param value Cookie value
 * @param options Cookie options
 */
export async function setCookie(name: string, value: string, options: CookieOptions = {}) {
  const cookieStore = await cookies();
  const cookieOptions = { ...defaultCookieOptions, ...options };

  try {
    await cookieStore.set(name, value, cookieOptions);

    if (process.env.NODE_ENV === 'development') {
      logger.debug(`Cookie set: ${name}`);
    }
  } catch (error) {
    console.error(`Error setting cookie ${name}:`, error);
  }
}

/**
 * Delete a cookie
 * @param name Cookie name
 * @param options Cookie options
 */
export async function deleteCookie(name: string, options: CookieOptions = {}) {
  const cookieStore = await cookies();
  const cookieOptions = { ...defaultCookieOptions, ...options };

  try {
    await cookieStore.delete(name);

    if (process.env.NODE_ENV === 'development') {
      logger.debug(`Cookie deleted: ${name}`);
    }
  } catch (error) {
    console.error(`Error deleting cookie ${name}:`, error);
  }
}

/**
 * Check if a cookie exists
 * @param name Cookie name
 * @returns True if the cookie exists
 */
export async function hasCookie(name: string) {
  const cookieStore = await cookies();
  return cookieStore.has(name);
}

/**
 * Copy cookies from one response to another
 * @param source Source response
 * @param target Target response
 * @param filter Optional filter function to select which cookies to copy
 */
export function copyCookies(
  source: NextResponse,
  target: NextResponse,
  filter?: (name: string) => boolean
) {
  source.cookies.getAll().forEach(cookie => {
    if (!filter || filter(cookie.name)) {
      target.cookies.set(cookie);
    }
  });
}

/**
 * Copy cookies from a request to a response
 * @param request Source request
 * @param response Target response
 * @param filter Optional filter function to select which cookies to copy
 */
export function copyCookiesFromRequest(
  request: NextRequest,
  response: NextResponse,
  filter?: (name: string) => boolean
) {
  request.cookies.getAll().forEach(cookie => {
    if (!filter || filter(cookie.name)) {
      response.cookies.set(cookie.name, cookie.value);
    }
  });
}

/**
 * Get all Supabase auth cookies
 * @returns All Supabase auth cookies
 */
export async function getSupabaseAuthCookies() {
  const cookieStore = await cookies();
  return cookieStore.getAll().filter(cookie => cookie.name.startsWith('sb-'));
}

/**
 * Check if Supabase auth cookies exist
 * @returns True if Supabase auth cookies exist
 */
export async function hasSupabaseAuthCookies() {
  const cookieStore = await cookies();
  return cookieStore.getAll().some(cookie => cookie.name.startsWith('sb-'));
}

/**
 * Copy Supabase auth cookies from one response to another
 * @param source Source response
 * @param target Target response
 */
export function copySupabaseAuthCookies(source: NextResponse, target: NextResponse) {
  copyCookies(source, target, name => name.startsWith('sb-'));
}

/**
 * Set the auth reset cookie
 * This cookie is used to indicate that the auth state has been reset
 */
export async function setAuthResetCookie() {
  await setCookie('sb-reset-complete', 'true', {
    maxAge: 60, // 1 minute
    path: '/',
  });
}

/**
 * Clear the auth reset cookie
 */
export async function clearAuthResetCookie() {
  await deleteCookie('sb-reset-complete');
}

/**
 * Check if the auth reset cookie exists
 * @returns True if the auth reset cookie exists
 */
export async function hasAuthResetCookie() {
  return await hasCookie('sb-reset-complete');
}
