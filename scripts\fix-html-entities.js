#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const rootDir = path.resolve(__dirname, '..');
const srcDir = path.join(rootDir, 'src');

// Statistics
let stats = {
  filesProcessed: 0,
  filesModified: 0,
  entitiesFixed: 0,
  errors: 0,
};

/**
 * Check if file should be processed
 */
function shouldProcessFile(filePath) {
  const ext = path.extname(filePath);
  return ['.tsx', '.jsx'].includes(ext) && 
         !filePath.includes('node_modules') && 
         !filePath.includes('.next') &&
         !filePath.includes('dist') &&
         !filePath.includes('build');
}

/**
 * Find all files to process
 */
function findFiles(dir) {
  let results = [];
  
  try {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        results = results.concat(findFiles(filePath));
      } else if (shouldProcessFile(filePath)) {
        results.push(filePath);
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
    stats.errors++;
  }
  
  return results;
}

/**
 * Fix malformed HTML entities and JSX syntax
 */
function fixHtmlEntities(content) {
  let modified = false;
  let fixCount = 0;
  
  // Fix malformed &quot; entities
  content = content.replace(/=&quot;([^"]*?)&quot;/g, (match, inner) => {
    modified = true;
    fixCount++;
    return `="${inner}"`;
  });
  
  // Fix malformed className attributes
  content = content.replace(/className=&quot;([^"]*?)&quot;/g, (match, inner) => {
    modified = true;
    fixCount++;
    return `className="${inner}"`;
  });
  
  // Fix malformed href attributes
  content = content.replace(/href=&quot;([^"]*?)&quot;/g, (match, inner) => {
    modified = true;
    fixCount++;
    return `href="${inner}"`;
  });
  
  // Fix malformed src attributes
  content = content.replace(/src=&quot;([^"]*?)&quot;/g, (match, inner) => {
    modified = true;
    fixCount++;
    return `src="${inner}"`;
  });
  
  // Fix malformed alt attributes
  content = content.replace(/alt=&quot;([^"]*?)&quot;/g, (match, inner) => {
    modified = true;
    fixCount++;
    return `alt="${inner}"`;
  });
  
  // Fix any remaining &quot; that should be regular quotes
  content = content.replace(/&quot;([^&]*?)&quot;/g, (match, inner) => {
    // Only replace if it's not inside an attribute
    if (!match.includes('=')) {
      modified = true;
      fixCount++;
      return `"${inner}"`;
    }
    return match;
  });
  
  // Fix malformed closing tags with spaces
  content = content.replace(/<\/(\w+)\s+>/g, (match, tagName) => {
    modified = true;
    fixCount++;
    return `</${tagName}>`;
  });
  
  // Fix self-closing tags with spaces
  content = content.replace(/<(\w+)([^>]*?)\s+\/>/g, (match, tagName, attrs) => {
    modified = true;
    fixCount++;
    return `<${tagName}${attrs} />`;
  });
  
  // Fix missing closing quotes in attributes
  content = content.replace(/(\w+)="([^"]*?)&quot;/g, (match, attr, value) => {
    modified = true;
    fixCount++;
    return `${attr}="${value}"`;
  });
  
  // Fix unterminated string literals in JSX
  content = content.replace(/="([^"]*?)$/gm, (match, value) => {
    modified = true;
    fixCount++;
    return `="${value}"`;
  });
  
  if (modified) {
    stats.entitiesFixed += fixCount;
  }
  
  return modified ? content : content;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    stats.filesProcessed++;
    
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // Apply fixes
    content = fixHtmlEntities(content);
    
    // Write back if modified
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      stats.filesModified++;
      console.log(`✅ Fixed: ${path.relative(rootDir, filePath)}`);
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    stats.errors++;
  }
}

/**
 * Main execution
 */
function main() {
  console.log('🚀 Starting HTML entity fixes...');
  console.log('📁 Source directory:', srcDir);
  console.log('');
  
  const files = findFiles(srcDir);
  console.log(`📄 Found ${files.length} files to check`);
  console.log('');
  
  files.forEach(processFile);
  
  console.log('');
  console.log('📊 SUMMARY:');
  console.log(`   Files processed: ${stats.filesProcessed}`);
  console.log(`   Files modified: ${stats.filesModified}`);
  console.log(`   HTML entities fixed: ${stats.entitiesFixed}`);
  console.log(`   Errors: ${stats.errors}`);
  console.log('');
  
  if (stats.errors === 0) {
    console.log('✅ All fixes completed successfully!');
  } else {
    console.log('⚠️  Some errors occurred during processing.');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, processFile };
