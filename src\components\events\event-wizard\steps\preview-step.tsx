'use client';

import { useState, useEffect } from 'react';
import { useWizard } from '@/components/events/event-wizard/wizard-container';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Check, AlertTriangle, Calendar, MapPin, DollarSign, Users,
  FileText, Tag, Clock, User, Share2, ExternalLink, Mail, Phone, Info
} from 'lucide-react';
import { getEventTypeById } from '@/app/actions/events';
import { EventType, EventField } from '@/types/event-types';
import Image from 'next/image';
import Link from 'next/link';
import { PreviewGallery } from '@/components/events/event-wizard/preview-gallery';
import { formatCurrency, formatPriceRange } from '@/lib/utils/currency-utils';

export function PreviewStep() {
  const { formData, updateFormData, submitForm } = useWizard();
  const [eventType, setEventType] = useState<EventType | null>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isValid, setIsValid] = useState(false);

  // We don't want to autofocus the submit button to prevent accidental submissions
  // Just add a visual indicator that the user needs to click the Create Event button

  // Fetch event type details
  useEffect(() => {
    const loadEventType = async () => {
      if (formData.eventTypeId) {
        const response = await getEventTypeById(formData.eventTypeId);
        if (response.success && response.data) {
          setEventType(response.data);
        }
      }
    };

    loadEventType();
  }, [formData.eventTypeId]);

  // Validate form data
  useEffect(() => {
    const errors: string[] = [];

    if (!formData.title || formData.title.length < 3) {
      errors.push('Event title is required and must be at least 3 characters');
    }

    if (!formData.description || formData.description.length < 10) {
      errors.push('Event description is required and must be at least 10 characters');
    }

    if (!formData.location || formData.location.length < 3) {
      errors.push('Event location is required');
    }

    if (!formData.state) {
      errors.push('State is required');
    }

    if (!formData.startDate) {
      errors.push('Start date is required');
    }

    // Validate categories for running events
    if (eventType?.slug === 'runs' && (!formData.categories || formData.categories.length === 0)) {
      errors.push('Running events require at least one category');
    }

    setValidationErrors(errors);
    setIsValid(errors.length === 0);

    // Update the submit button state
    const submitButton = document.getElementById('wizard-submit-button') as HTMLButtonElement;
    if (submitButton) {
      submitButton.disabled = errors.length > 0;
    }
  }, [formData, eventType]);

  // Format date for display
  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not set';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Format price for display based on event country
  const formatPrice = (price?: number) => {
    return formatCurrency(price, formData.country);
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className=&quot;text-2xl font-semibold mb-2">Review and Create Event</h2>
        <p className="text-[hsl(var(--muted-foreground))]">
          Preview how your event will appear to attendees
        </p>
      </div>

      {/* Validation Errors */}
      {validationErrors.length > 0 && (
        <Alert variant=&quot;destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Validation Errors</AlertTitle>
          <AlertDescription>
            <ul className=&quot;list-disc list-inside text-sm mt-2">
              {validationErrors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Success Message */}
      {isValid && (
        <Alert className="bg-[hsl(var(--success))/10] border-[hsl(var(--success))/20]">
          <Check className=&quot;h-4 w-4 text-[hsl(var(--success))]" />
          <AlertTitle className="text-[hsl(var(--success))]">Ready to Create</AlertTitle>
          <AlertDescription className=&quot;text-[hsl(var(--success))]">
            Your event is ready to be created.
          </AlertDescription>
        </Alert>
      )}

      {/* Event Preview - Public View */}
      <Card className="bg-[hsl(var(--card))]">
        <CardHeader>
          <CardTitle className=&quot;flex items-center gap-2">
            <Info className="h-4 w-4" />
            Public Event Page Preview
          </CardTitle>
          <CardDescription>This is how your event will appear to attendees</CardDescription>
        </CardHeader>
        <CardContent className=&quot;p-0 overflow-hidden">
          <div className="border-t border-t-[hsl(var(--border))]">
            {/* Facebook-style cover banner */}
            <div className=&quot;relative w-full h-[200px] md:h-[300px] overflow-hidden bg-[hsl(var(--muted))]">
              {formData.coverImage ? (
                <Image
                  src={formData.coverImage.url}
                  alt={formData.title || "Event cover"}
                  fill
                  className="object-cover"
                  style={formData.coverImage.focusPoint ? {
                    objectPosition: `center ${formData.coverImage.focusPoint.y}%`
                  } : {}} />
              ) : (
                <div className=&quot;absolute inset-0 flex items-center justify-center bg-[hsl(var(--muted))]">
                  <p className="text-[hsl(var(--muted-foreground))] text-sm">No cover image uploaded</p>
                </div>
              )}

              <div className=&quot;absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/70 dark:to-black/90" />

              <div className="absolute bottom-0 left-0 right-0 p-6 text-white">
                <div className=&quot;container mx-auto">
                  <Badge className="mb-3">{eventType?.name || "Event"}</Badge>
                  <h1 className="text-2xl md:text-3xl font-bold mb-2">{formData.title || 'Untitled Event'}</h1>
                  <div className=&quot;flex flex-wrap gap-4 text-sm">
                    {formData.startDate && (
                      <div className="flex items-center gap-1">
                        <Calendar className=&quot;h-4 w-4" />
                        <span>{formatDate(formData.startDate)}</span>
                      </div>
                    )}

                    {formData.startDate && (
                      <div className="flex items-center gap-1">
                        <Clock className=&quot;h-4 w-4" />
                        <span>{new Date(formData.startDate).toLocaleTimeString()}</span>
                      </div>
                    )}

                    {formData.location && (
                      <div className="flex items-center gap-1">
                        <MapPin className=&quot;h-4 w-4" />
                        <span>
                          {formData.location}
                          {formData.city && `, ${formData.city}`}
                          {formData.state && `, ${formData.state}`}
                        </span>
                      </div>
                    )}

                    <div className="flex items-center gap-1">
                      <User className=&quot;h-4 w-4" />
                      <span>Your Organization</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="p-6 bg-[hsl(var(--background))]">
              <div className=&quot;grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="md:col-span-2 space-y-8">
                  {/* Event description */}
                  <div>
                    <h2 className=&quot;text-2xl font-semibold mb-4">About This Event</h2>
                    <p className="text-[hsl(var(--foreground))] whitespace-pre-line">
                      {formData.description || 'No description provided'}
                    </p>
                  </div>

                  {/* Event gallery */}
                  <div>
                    <h2 className=&quot;text-2xl font-semibold mb-4">Event Gallery</h2>
                    <PreviewGallery
                      posterImage={formData.posterImage}
                      className="w-full" />
                  </div>

                  {/* T-Shirt Options section removed to avoid duplication - kept in the Organizer-Only Information section */}
                </div>

                <div className=&quot;space-y-6">
                  {/* Price & ticket info */}
                  <div className="bg-[hsl(var(--muted))] rounded-lg p-6 border border-[hsl(var(--border))]">
                    <h3 className=&quot;text-xl font-semibold mb-3">
                      {formData.categories && formData.categories.length > 0 ? (
                        (() => {
                          // Get all prices (excluding undefined/null values)
                          const prices = formData.categories
                            .map((c: any) => c.properties?.price || c.price) // Try to get price from properties first, then fallback to direct price
                            .filter((price: unknown) => price !== undefined && price !== null);

                          // If no valid prices, show Free Entry
                          if (prices.length === 0) return 'Free Entry';

                          // If all prices are the same, show single price
                          const uniquePrices = [...new Set(prices)];
                          if (uniquePrices.length === 1 && typeof uniquePrices[0] === 'number') {
                            return formatCurrency(uniquePrices[0], formData.country);
                          }

                          // Otherwise show price range
                          const minPrice = Math.min(...prices);
                          const maxPrice = Math.max(...prices);
                          return formatPriceRange(minPrice, maxPrice, formData.country);
                        })()
                      ) : (
                        'Free Entry'
                      )}
                    </h3>

                    <Button className="w-full" asChild>
                      <span>
                        <ExternalLink className=&quot;h-4 w-4 mr-2" />
                        Book Tickets
                      </span>
                    </Button>

                    <div className="mt-4 flex justify-between">
                      <Button variant=&quot;outline" size="sm"/>
                        <Share2 className=&quot;h-4 w-4 mr-2" />
                        Share
                      </Button>
                      <Button variant="outline" size=&quot;sm"/>
                        Add to Calendar
                      </Button>
                    </div>
                  </div>

                  {/* Organizer info */}
                  <div className="bg-[hsl(var(--muted))] rounded-lg p-6 border border-[hsl(var(--border))]">
                    <h3 className=&quot;text-lg font-semibold mb-3">Organizer</h3>
                    <p className="font-medium mb-3">Your Organization</p>

                    <Separator className=&quot;my-3" />

                    <div className="space-y-2">
                      <div className=&quot;flex items-center gap-2 text-sm">
                        <Mail className="h-4 w-4 text-[hsl(var(--muted-foreground))]" />
                        <span className=&quot;text-[hsl(var(--primary))]"><EMAIL></span>
                      </div>

                      <div className="flex items-center gap-2 text-sm">
                        <Phone className=&quot;h-4 w-4 text-[hsl(var(--muted-foreground))]" />
                        <span className="text-[hsl(var(--primary))]">+****************</span>
                      </div>
                    </div>
                  </div>

                  {/* Location info */}
                  <div className=&quot;bg-[hsl(var(--muted))] rounded-lg p-6 border border-[hsl(var(--border))]">
                    <h3 className="text-lg font-semibold mb-3">Location</h3>
                    <p className=&quot;mb-2">{formData.location || 'TBD'}</p>
                    <p className="text-[hsl(var(--foreground))]">
                      {formData.city && `${formData.city}, `}
                      {formData.state}
                      {formData.country && `, ${formData.country}`}
                    </p>

                    <Button variant=&quot;outline" className="w-full mt-4" asChild>
                      <span>
                        <MapPin className=&quot;h-4 w-4 mr-2" />
                        View on Map
                      </span>
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Organizer-Only Information */}
      <Card className="bg-[hsl(var(--card))]">
        <CardHeader>
          <CardTitle className=&quot;flex items-center gap-2">
            <Info className="h-4 w-4" />
            Organizer-Only Information
          </CardTitle>
          <CardDescription>This information is only visible to you as the organizer</CardDescription>
        </CardHeader>
        <CardContent className=&quot;bg-[hsl(var(--card))]">
          <div className="space-y-6">
            {/* Event Summary */}
            <div className=&quot;grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-[hsl(var(--muted))] p-4 rounded-md border border-[hsl(var(--border))]">
                <h3 className=&quot;font-medium mb-2">Event Type</h3>
                <p>{eventType?.name || 'Unknown'}</p>
              </div>

              <div className="bg-[hsl(var(--muted))] p-4 rounded-md border border-[hsl(var(--border))]">
                <h3 className=&quot;font-medium mb-2">Categories</h3>
                <p>{formData.categories?.length || 0} categories defined</p>
              </div>

              <div className="bg-[hsl(var(--muted))] p-4 rounded-md border border-[hsl(var(--border))]">
                <h3 className=&quot;font-medium mb-2">Custom Fields</h3>
                <p>{formData.customFields?.length || 0} fields defined</p>
              </div>

              <div className="bg-[hsl(var(--muted))] p-4 rounded-md border border-[hsl(var(--border))]">
                <h3 className=&quot;font-medium mb-2">T-Shirt Options</h3>
                <p>{formData.tshirtOptions?.enabled ?
                  `${formData.tshirtOptions.sizes?.length || 0} sizes available` :
                  'Not enabled'}</p>
              </div>
            </div>

            {/* Categories */}
            {formData.categories && formData.categories.length > 0 && (
              <div className="bg-[hsl(var(--card))]">
                <h3 className=&quot;text-lg font-medium mb-3">Event Categories</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 bg-[hsl(var(--card))]">
                  {formData.categories.map((category: any, index: number) => (
                    <Card key={index} className=&quot;border border-[hsl(var(--border))] shadow-none bg-[hsl(var(--card))]">
                      <CardContent className="p-4">
                        <div className=&quot;flex justify-between items-start mb-2">
                          <h4 className="font-semibold">{category.name}</h4>
                          <div className=&quot;bg-[hsl(var(--primary-50))] text-[hsl(var(--primary))] text-xs font-medium px-2.5 py-0.5 rounded">
                            {formatCurrency(category.properties?.price || category.price, formData.country)}
                          </div>
                        </div>

                        {category.description && (
                          <p className="text-sm text-[hsl(var(--muted-foreground))] mb-2">{category.description}</p>
                        )}

                        <div className=&quot;grid grid-cols-2 gap-1 text-xs text-[hsl(var(--muted-foreground))]">
                          {(category.properties?.capacity || category.capacity) && (
                            <div className="flex items-center gap-1">
                              <Users className=&quot;h-3 w-3 text-[hsl(var(--muted-foreground))]" />
                              <span>Capacity: {category.properties?.capacity || category.capacity}</span>
                            </div>
                          )}

                          {(category.properties?.registrationLimit || category.registrationLimit) && (
                            <div className="flex items-center gap-1">
                              <FileText className=&quot;h-3 w-3 text-[hsl(var(--muted-foreground))]" />
                              <span>Reg. Limit: {category.properties?.registrationLimit || category.registrationLimit}</span>
                            </div>
                          )}

                          {(category.properties?.startTime || category.startTime) && (
                            <div className="flex items-center gap-1">
                              <Clock className=&quot;h-3 w-3 text-[hsl(var(--muted-foreground))]" />
                              <span>Start Time: {category.properties?.startTime || category.startTime}</span>
                            </div>
                          )}

                          {(category.properties?.earlyBirdPrice || category.earlyBirdPrice) && (
                            <div className="flex items-center gap-1">
                              <DollarSign className=&quot;h-3 w-3 text-[hsl(var(--muted-foreground))]" />
                              <span>Early Bird: {formatCurrency(category.properties?.earlyBirdPrice || category.earlyBirdPrice, formData.country)}</span>
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Custom Fields */}
            {formData.customFields && formData.customFields.length > 0 && (
              <div className="bg-[hsl(var(--card))]">
                <h3 className=&quot;text-lg font-medium mb-3">Custom Registration Fields</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 bg-[hsl(var(--card))]">
                  {formData.customFields.map((field: EventField, index: number) => (
                    <div key={index} className=&quot;border border-[hsl(var(--border))] rounded-md p-3 bg-[hsl(var(--card))]">
                      <div className="font-medium flex items-center justify-between">
                        <span>{field.label}</span>
                        {field.required && (
                          <span className=&quot;text-[hsl(var(--destructive))] text-xs">Required</span>
                        )}
                      </div>
                      <div className="text-xs text-[hsl(var(--muted-foreground))] mt-1">
                        {field.type}
                        {field.description && (
                          <p className=&quot;mt-1">{field.description}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* T-Shirt Options */}
            {formData.tshirtOptions && formData.tshirtOptions.enabled && (
              <div className="bg-[hsl(var(--card))] mt-6">
                <h3 className=&quot;text-lg font-medium mb-3">T-Shirt Options</h3>
                <Card className="border border-[hsl(var(--border))] shadow-none bg-[hsl(var(--card))]">
                  <CardContent className=&quot;p-4">
                    {formData.tshirtOptions.description && (
                      <div className="mb-4">
                        <h4 className=&quot;font-semibold mb-2">Description</h4>
                        <p className="text-sm text-[hsl(var(--muted-foreground))]">
                          {formData.tshirtOptions.description}
                        </p>
                      </div>
                    )}

                    <div className=&quot;mb-4">
                      <h4 className="font-semibold mb-2">Available Sizes</h4>
                      <div className=&quot;flex flex-wrap gap-2">
                        {formData.tshirtOptions.sizes.map((size: string, index: number) => (
                          <Badge key={index} variant="outline" className="bg-[hsl(var(--muted))]">
                            {size}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {formData.tshirtOptions.sizeChartImage && (
                      <div className=&quot;mt-4 w-full">
                        <h4 className="font-semibold mb-2">Size Chart</h4>
                        <div className=&quot;relative w-full rounded-md overflow-hidden border border-[hsl(var(--border))]">
                          <div className="relative w-full h-auto aspect-auto min-h-[300px]">
                            <Image
                              src={formData.tshirtOptions.sizeChartImage.url}
                              alt="T-shirt Size Chart"
                              fill
                              className="object-contain"
                              sizes=&quot;100vw"
                              priority />
                          </div>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}