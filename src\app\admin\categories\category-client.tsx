'use client';

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { Pencil, Plus, Trash } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"

type Category = {
  id: string
  name: string
  description: string
}

export function CategoryClient() {
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)
  const { toast } = useToast()

  const fetchCategories = async () => {
    try {
      setIsLoading(true)
      const response = await fetch("/api/admin/categories")
      const data = await response.json()
      setCategories(data)
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to fetch categories",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const name = formData.get("name") as string
    const description = formData.get("description") as string

    try {
      setIsLoading(true)
      const response = await fetch("/api/admin/categories", {
        method: selectedCategory ? "PUT" : "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          id: selectedCategory?.id,
          name,
          description,
        }),
      })

      if (!response.ok) throw new Error()

      toast({
        title: "Success",
        description: `Category ${selectedCategory ? "updated" : "created"} successfully`,
      })
      fetchCategories()
      setSelectedCategory(null)
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${selectedCategory ? "update" : "create"} category`,
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async (id: string) => {
    if (!confirm("Are you sure you want to delete this category?")) return

    try {
      setIsLoading(true)
      const response = await fetch(`/api/admin/categories?id=${id}`, {
        method: "DELETE",
      })

      if (!response.ok) throw new Error()

      toast({
        title: "Success",
        description: "Category deleted successfully",
      })
      fetchCategories()
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete category",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Categories</h2>
        <Dialog>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Add Category
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {selectedCategory ? "Edit Category" : "Add Category"}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name"/>Name</Label>
                <Input
                  id="name"
                  name="name&quot;
                  required
                  defaultValue={selectedCategory?.name}
                //>
              </div>
              <div className="space-y-2">
                <Label htmlFor="description"/>Description</Label>
                <Textarea
                  id="description"
                  name="description&quot;
                  required
                  defaultValue={selectedCategory?.description}
                />
              </div>
              <Button type="submit" disabled={isLoading}/>
                {selectedCategory ? "Update" : "Create"}
              </Button>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {categories.map((category) => (
          <div
            key={category.id}
            className="rounded-lg border p-4 space-y-2"
          >
            <div className="flex items-center justify-between">
              <h3 className="font-semibold">{category.name}</h3>
              <div className="space-x-2">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() =/> setSelectedCategory(category)}
                >
                  <Pencil className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() =/> handleDelete(category.id)}
                >
                  <Trash className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <p className="text-sm text-muted-foreground">
              {category.description}
            </p>
          </div>
        ))}
      </div>
    </div>
  )
} 