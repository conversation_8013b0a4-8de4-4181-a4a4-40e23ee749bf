'use server';

import { createClient } from '@/lib/supabase/server';
import { EventService, createEventSchema } from '@/lib/services/event-service';
import { revalidatePath } from 'next/cache';
import { z } from 'zod';

/**
 * Create a new event
 */
export async function createEvent(formData: FormData) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user?.id) {
      return { error: 'Unauthorized' };
    }

    // Parse and validate form data
    const rawData = Object.fromEntries(formData.entries());

    // Convert numeric values
    const parsedData = {
      ...rawData,
      ticketTypes: rawData.ticketTypes
        ? JSON.parse(rawData.ticketTypes as string)
        : undefined,
    };

    // Validate with Zod
    const data = createEventSchema.parse(parsedData);

    // Create event using service
    const eventService = new EventService();
    const event = await eventService.createEvent(data);

    if (!event) {
      return { error: 'Failed to create event' };
    }

    // Revalidate relevant paths
    revalidatePath('/dashboard/events');

    return { success: true, eventId: event.id };
  } catch (error) {
    console.error('Error creating event:', error);
    if (error instanceof z.ZodError) {
      return { error: 'Invalid form data', details: error.errors?.map(e => ({ path: e.path, message.message })) };
    }
    return { error: 'Failed to create event' };
  }
}

/**
 * Publish an event
 */
export async function publishEvent(eventId: string) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user?.id) {
      return { error: 'Unauthorized' };
    }

    const eventService = new EventService();
    const event = await eventService.publishEvent(eventId);

    if (!event) {
      return { error: 'Failed to publish event' };
    }

    // Revalidate relevant paths
    revalidatePath('/dashboard/events');
    revalidatePath(`/events/${eventId}`);

    return { success: true };
  } catch (error) {
    console.error('Error publishing event:', error);
    return { error: 'Failed to publish event' };
  }
}

/**
 * Cancel an event
 */
export async function cancelEvent(eventId: string) {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user?.id) {
      return { error: 'Unauthorized' };
    }

    const eventService = new EventService();
    const event = await eventService.cancelEvent(eventId);

    if (!event) {
      return { error: 'Failed to cancel event' };
    }

    // Revalidate relevant paths
    revalidatePath('/dashboard/events');
    revalidatePath(`/events/${eventId}`);

    return { success: true };
  } catch (error) {
    console.error('Error cancelling event:', error);
    return { error: 'Failed to cancel event' };
  }
}

/**
 * Get event dashboard data
 */
export async function getEventDashboardData() {
  try {
    const supabase = await createClient();
    const { data: { user } } = await supabase.auth.getUser();
    if (!user?.id) {
      return { error: 'Unauthorized' };
    }

    const eventService = new EventService();
    const data = await eventService.getEventDashboardData();

    return { success: true, data };
  } catch (error) {
    console.error('Error getting event dashboard data:', error);
    return { error: 'Failed to get event dashboard data' };
  }
}
