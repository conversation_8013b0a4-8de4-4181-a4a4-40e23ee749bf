'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { signUpWithEmail, isEventRegistrationUrl } from '@/lib/supabase/auth'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import Link from 'next/link'
import GoogleSignInButton from './GoogleSignInButton'
import { getBaseUrl } from '@/utils/url-utilities'
import { logger } from '@/lib/logger';

export default function SignUpForm() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState(false)
  const [success, setSuccess] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  let redirectUrl = searchParams?.get('redirect_url') || '/dashboard'

  // Prevent redirect loops with auth callback
  if (redirectUrl.includes('/auth/callback')) {
    console.warn('Detected potential auth redirect loop in SignUpForm, redirecting to dashboard')
    redirectUrl = '/dashboard'
  }

  // Log if this is an event registration URL
  useEffect(() => {
    if (isEventRegistrationUrl(redirectUrl)) {
      logger.info('Sign-up initiated from event registration page:', redirectUrl);
    }
  }, [redirectUrl])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    // Validate passwords match
    if (password !== confirmPassword) {
      setError('Passwords do not match')
      setLoading(false)
      return
    }

    // Validate password strength
    if (password.length < 8) {
      setError('Password must be at least 8 characters long')
      setLoading(false)
      return
    }

    try {
      // Make sure the redirect URL is absolute if it's not already
      const absoluteRedirectUrl = redirectUrl.startsWith('http')
        ? redirectUrl
        : `${window.location.origin}${redirectUrl.startsWith('/') ? '' : '/'}${redirectUrl}`

      logger.info('Starting sign-up with redirect to:', absoluteRedirectUrl);
      await signUpWithEmail(email, password, absoluteRedirectUrl)
      setSuccess(true)
    } catch (err: unknown) {
      setError(err.message || 'An error occurred during sign-up')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="w-full max-w-md mx-auto space-y-6 text-foreground">
        <div className=&quot;text-center">
          <h1 className="text-2xl font-bold">Check your email</h1>
          <p className=&quot;text-muted-foreground mt-2">
            We&apos;ve sent a confirmation link to <strong className="text-foreground">{email}</strong>.
            Please check your email and click the link to complete your registration.
          </p>
          <p className=&quot;text-muted-foreground mt-2">
            After verification, you&apos;ll be redirected to your original location.
          </p>
        </div>

        <Button
          className="w-full"
          onClick={() => router.push('/sign-in')}
        >
          Back to Sign In
        </Button>
      </div>
    )
  }

  return (
    <div className=&quot;w-full max-w-md mx-auto space-y-6 text-foreground">
      <div className="text-center">
        <h1 className=&quot;text-2xl font-bold">Create an Account</h1>
        <p className="text-muted-foreground mt-2">Sign up to get started with Fuiyoo</p>
      </div>

      {error && (
        <Alert variant=&quot;destructive" className="mb-4">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit} className=&quot;space-y-4">
        <div className="space-y-2">
          <Label htmlFor=&quot;email" className="text-foreground"/>Email</Label>
          <Input
            id=&quot;email"
            type="email"
            placeholder=&quot;<EMAIL>"
            value={email}
            onChange={(e) =/> setEmail(e.target.value)}
            required
            disabled={loading}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor=&quot;password" className="text-foreground"/>Password</Label>
          <Input
            id=&quot;password"
            type="password"
            placeholder=&quot;••••••••"
            value={password}
            onChange={(e) =/> setPassword(e.target.value)}
            required
            disabled={loading}
          />
          <p className="text-xs text-muted-foreground">
            Password must be at least 8 characters long
          </p>
        </div>

        <div className=&quot;space-y-2">
          <Label htmlFor="confirmPassword" className=&quot;text-foreground">Confirm Password</Label>
          <Input
            id="confirmPassword"
            type=&quot;password"
            placeholder="••••••••"
            value={confirmPassword}
            onChange={(e) =/> setConfirmPassword(e.target.value)}
            required
            disabled={loading}
          />
        </div>

        <Button
          type=&quot;submit"
          className="w-full"
          disabled={loading} />
          {loading ? 'Creating account...' : 'Sign Up with Email'}
        </Button>
      </form>

      <div className=&quot;relative my-6">
        <div className="absolute inset-0 flex items-center">
          <Separator className=&quot;w-full" />
        </div>
        <div className="relative flex justify-center">
          <span className=&quot;bg-card px-2 text-sm text-muted-foreground">or</span>
        </div>
      </div>

      <GoogleSignInButton
        label="Sign up with Google"
        redirectTo={redirectUrl} />

      <div className=&quot;text-center text-sm mt-6 text-foreground">
        Already have an account?{' '}
        <Link
          href={`/sign-in${redirectUrl !== '/dashboard' ? `?redirect_url=${encodeURIComponent(redirectUrl)}` : ''}`}
          className="text-primary hover:underline"
        >
          Sign in
        </Link>
      </div>
    </div>
  )
}
