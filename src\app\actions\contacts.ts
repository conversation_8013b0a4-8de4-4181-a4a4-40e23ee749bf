'use server';

import { createServerActionClient } from "@/lib/supabase/actions";
import { revalidatePath } from "next/cache";
import { ContactSchema, type ContactFormData } from "@/lib/schemas/contacts";
import { createAdminClient } from '@/lib/supabase/admin-client';
import { logger } from '@/lib/logger';

/**
 * KNOWN ISSUE: There is a problem with the Supabase schema cache not recognizing the emergency contact fields
 * in the saved_contacts table. This causes the error:
 * "Could not find the 'emergency_contact_name' column of 'saved_contacts' in the schema cache"
 *
 * TEMPORARY WORKAROUND: The current solution is to omit these fields when creating or updating contacts.
 *
 * PERMANENT FIX TODO:
 * 1. Run a migration to refresh the schema cache (migration file created: 0010_fix_emergency_contact_fields.sql)
 * 2. Alternatively, use the Supabase Studio to manually run SQL to fix the schema cache:
 *    ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_name TYPE TEXT;
 *    ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_no TYPE TEXT;
 *    ALTER TABLE saved_contacts ALTER COLUMN emergency_contact_relationship TYPE TEXT;
 * 3. Once the schema cache is fixed, restore the emergency contact fields in the insert/update operations
 */

/**
 * Get all saved contacts for the current user
 */
export async function getSavedContacts() {
  try {
    const supabase = await createServerActionClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user?.id) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID (not the auth_user_id)
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', user.id)
      .single();

    if (userError || !userData) {
      console.error("Error fetching user ID:", userError);
      throw new Error("User not found in database");
    }

    const userId = userData.id;

    const { data, error } = await supabase
      .from('saved_contacts')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      throw new Error(`Error fetching contacts: ${error.message}`);
    }

    return { contacts: data };
  } catch (error) {
    console.error("Error in getSavedContacts:", error);
    return { error instanceof Error ? error.message : "Unknown error" };
  }
}

/**
 * Get a single contact by ID
 */
export async function getContactById(contactId: string) {
  try {
    const supabase = await createServerActionClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user?.id) {
      throw new Error("Unauthorized");
    }

    const userId = session.user.id;

    const { data, error } = await supabase
      .from('saved_contacts')
      .select('*')
      .eq('id', contactId)
      .eq('user_id', userId)
      .single();

    if (error) {
      throw new Error(`Error fetching contact: ${error.message}`);
    }

    return { contact: data };
  } catch (error) {
    console.error("Error in getContactById:", error);
    return { error instanceof Error ? error.message : "Unknown error" };
  }
}

/**
 * Create a new saved contact
 */
export async function createContact(formData: ContactFormData) {
  try {
    const supabase = await createServerActionClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user?.id) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID (not the auth_user_id)
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', session.user.id)
      .single();

    if (userError || !userData) {
      console.error("Error fetching user ID:", userError);
      throw new Error("User not found in database");
    }

    const userId = userData.id;

    // Clone the form data and ensure userId is set
    const validationData = {
      ...formData,
      userId
    };

    logger.info("Server-side validation data:", validationData);
    // Validate form data
    const result = ContactSchema.safeParse(validationData);

    if (!result.success) {
      const errorMessage = result.error.errors?.map(e => `${e.path.join('.')}: ${e.message}`).join(', ') || 'Invalid input data';
      throw new Error(`Validation failed: ${errorMessage}`);
    }

    // TEMPORARY SOLUTION: Use regular Supabase query instead of direct SQL execution
    // Supabase client already created above

    // Insert the contact using the standard Supabase API
    const { data, error } = await supabase
      .from('saved_contacts')
      .insert({
        user_id: userId,
        first_name: formData.firstName,
        last_name: formData.lastName || null,
        // Combine first_name and last_name for the full_name field to maintain backward compatibility
        // @ts-expect-error - full_name exists in the database schema but might not be in the TypeScript types
        full_name: `${formData.firstName}${formData.lastName ? ' ' + formData.lastName : ''}`,
        relationship: formData.relationship,
        email: formData.email || null,
        phone: formData.phone || null,
        date_of_birth: formData.dateOfBirth || null,
        gender: formData.gender || null,
        tshirt_size: formData.tShirtSize || null,
        address: formData.address || null,
        city: formData.city || null,
        state: formData.state || null,
        country: formData.country || null,
        postcode: formData.postcode || null,
        emergency_contact_name: formData.emergencyContactName || null,
        emergency_contact_no: formData.emergencyContactNo || null,
        emergency_contact_relationship: formData.emergencyContactRelationship || null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error("Error executing Supabase insert:", error);
      throw new Error(`Failed to create contact: ${error.message}`);
    }

    // Revalidate path to update UI
    revalidatePath('/dashboard/profile/contacts');
    return { success: true, contact: data };

  } catch (error) {
    console.error("Error in createContact:", error);
    return { error instanceof Error ? error.message : "Unknown error" };
  }
}

/**
 * Update an existing saved contact
 */
export async function updateContact(contactId: string, formData: ContactFormData) {
  try {
    const supabase = await createServerActionClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user?.id) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID (not the auth_user_id)
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', session.user.id)
      .single();

    if (userError || !userData) {
      console.error("Error fetching user ID:", userError);
      throw new Error("User not found in database");
    }

    const userId = userData.id;

    // Clone the form data and ensure userId is set
    const validationData = {
      ...formData,
      userId
    };

    logger.info("Server-side validation data for update:", validationData);
    // Validate form data
    const result = ContactSchema.safeParse(validationData);

    if (!result.success) {
      const errorMessage = result.error.errors?.map(e => `${e.path.join('.')}: ${e.message}`).join(', ') || 'Invalid input data';
      throw new Error(`Validation failed: ${errorMessage}`);
    }

    // Supabase client already created above

    // First verify the contact belongs to the user
    const { data: existingContact, error: fetchError } = await supabase
      .from('saved_contacts')
      .select('id')
      .eq('id', contactId)
      .eq('user_id', userId)
      .single();

    if (fetchError || !existingContact) {
      throw new Error("Contact not found or you don't have permission to update it");
    }

    // Include emergency contact fields now that we've fixed the schema
    const { data, error } = await supabase
      .from('saved_contacts')
      .update({
        first_name: formData.firstName,
        last_name: formData.lastName,
        // Update full_name to match first_name and last_name
        // @ts-expect-error - full_name exists in the database schema but might not be in the TypeScript types
        full_name: `${formData.firstName}${formData.lastName ? ' ' + formData.lastName : ''}`,
        relationship: formData.relationship,
        email: formData.email,
        phone: formData.phone,
        date_of_birth: formData.dateOfBirth,
        gender: formData.gender,
        tshirt_size: formData.tShirtSize,
        address: formData.address,
        city: formData.city,
        state: formData.state,
        country: formData.country,
        postcode: formData.postcode,
        emergency_contact_name: formData.emergencyContactName,
        emergency_contact_no: formData.emergencyContactNo,
        emergency_contact_relationship: formData.emergencyContactRelationship,
        updated_at: new Date().toISOString(),
      })
      .eq('id', contactId)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(`Error updating contact: ${error.message}`);
    }

    revalidatePath('/dashboard/profile/contacts');
    return { contact: data };
  } catch (error) {
    console.error("Error in updateContact:", error);
    return { error instanceof Error ? error.message : "Unknown error" };
  }
}

/**
 * Delete a saved contact
 */
export async function deleteContact(contactId: string) {
  try {
    const supabase = await createServerActionClient();
    const { data: { session } } = await supabase.auth.getSession();

    if (!session?.user?.id) {
      throw new Error("Unauthorized");
    }

    // Get the database user ID (not the auth_user_id)
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('auth_user_id', session.user.id)
      .single();

    if (userError || !userData) {
      console.error("Error fetching user ID:", userError);
      throw new Error("User not found in database");
    }

    const userId = userData.id;

    // First verify the contact belongs to the user
    const { data: existingContact, error: fetchError } = await supabase
      .from('saved_contacts')
      .select('id')
      .eq('id', contactId)
      .eq('user_id', userId)
      .single();

    if (fetchError || !existingContact) {
      throw new Error("Contact not found or you don't have permission to delete it");
    }

    const { error } = await supabase
      .from('saved_contacts')
      .delete()
      .eq('id', contactId)
      .eq('user_id', userId);

    if (error) {
      throw new Error(`Error deleting contact: ${error.message}`);
    }

    revalidatePath('/dashboard/profile/contacts');
    return { success: true };
  } catch (error) {
    console.error("Error in deleteContact:", error);
    return { error instanceof Error ? error.message : "Unknown error" };
  }
}