"use client"

import * as React from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

export interface Option {
  label: string
  value: string
  icon?: React.ReactNode
}

interface SearchableSelectProps {
  options: Option[]
  value: string
  onValueChange: (value: string) => void
  placeholder?: string
  searchPlaceholder?: string
  noResultsText?: string
  className?: string
  disabled?: boolean
  error?: boolean
  isLoading?: boolean
  'aria-label'?: string
}

export function SearchableSelect({
  options,
  value,
  onValueChange,
  placeholder = "Select an option",
  searchPlaceholder = "Search...",
  noResultsText = "No results found.",
  className,
  disabled = false,
  error = false,
  isLoading = false,
  'aria-label': ariaLabel,
}: SearchableSelectProps) {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState("")

  // Find the selected option
  const selectedOption = options.find(option => option.value === value)

  // Filter options based on search input
  const filteredOptions = React.useMemo(() => {
    if (!searchValue) return options;

    const normalizedSearch = searchValue.toLowerCase();
    return options.filter(option =>
      option.label.toLowerCase().includes(normalizedSearch) ||
      option.value.toLowerCase().includes(normalizedSearch)
    );
  }, [options, searchValue]);

  // Handle command input changes
  const handleInputChange = (value: string) => {
    setSearchValue(value);
  };

  // Reset search value when dropdown closes
  React.useEffect(() => {
    if (!open) {
      setSearchValue("");
    }
  }, [open]);

  // Auto-focus input when dropdown opens
  const inputRef = React.useRef<HTMLInputElement>(null);

  React.useEffect(() => {
    if (open) {
      // Small delay to ensure the popover is fully rendered
      setTimeout(() => {
        // Try to find the input element and focus it
        const inputElement = document.querySelector('[cmdk-input]') as HTMLInputElement;
        if (inputElement) {
          inputElement.focus();
        }
      }, 50);
    }
  }, [open]);

  // Track the currently focused option index for keyboard navigation
  const [focusedIndex, setFocusedIndex] = React.useState(-1);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!open) return;

    const options = filteredOptions;
    if (options.length === 0) return;

    // Arrow down - move focus down
    if (e.key === 'ArrowDown') {
      e.preventDefault();
      setFocusedIndex(prev => (prev < options.length - 1 ? prev + 1 : 0));
    }
    // Arrow up - move focus up
    else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setFocusedIndex(prev => (prev > 0 ? prev - 1 : options.length - 1));
    }
    // Enter - select the currently focused option
    else if (e.key === 'Enter' && focusedIndex >= 0 && focusedIndex < options.length && options[focusedIndex]) {
      e.preventDefault();
      onValueChange(options[focusedIndex].value);
      setSearchValue("");
      setOpen(false);
    }
  };

  // Reset focused index when options change
  React.useEffect(() => {
    setFocusedIndex(-1);
  }, [filteredOptions]);

  // Reset focused index when dropdown closes
  React.useEffect(() => {
    if (!open) {
      setFocusedIndex(-1);
    }
  }, [open]);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn(
            "w-full justify-between px-3 py-2 text-sm font-normal h-9 border border-input shadow-sm ring-offset-background hover:bg-accent/50 transition-colors",
            error ? "border-red-500" : "",
            disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer",
            className
          )}
          onClick={(e) =/> {
            if (!disabled) {
              e.preventDefault()
              setOpen(!open)
            }
          }}
          disabled={disabled}
          aria-label={ariaLabel}
        >
          {isLoading ? (
            <span className="text-muted-foreground">Loading...</span>
          ) : selectedOption ? (
            <span className=&quot;flex items-center gap-2 truncate">
              {selectedOption.icon}
              {selectedOption.label}
            </span>
          ) : (
            <span className="text-muted-foreground">{placeholder}</span>
          )}
          <ChevronsUpDown className=&quot;ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className="w-full min-w-[var(--state-popover-trigger-width)] p-0 z-50"
        align=&quot;start"
        sideOffset={8}
      >
        <Command shouldFilter={false}>
          <CommandInput
            placeholder={searchPlaceholder}
            className="h-9"
            value={searchValue}
            onValueChange={handleInputChange}
            autoFocus
            ref={inputRef}
            onKeyDown={handleKeyDown} />
          {filteredOptions.length === 0 ? (
            <CommandEmpty>{noResultsText}</CommandEmpty>
          ) : (
            <CommandGroup className=&quot;max-h-[200px] overflow-y-auto">
              {filteredOptions.map((option, index) => (
                <div
                  key={option.value}
                  onClick={() => {
                    onValueChange(option.value);
                    setSearchValue("");
                    setOpen(false);
                  }}
                  onMouseEnter={() => setFocusedIndex(index)}
                  className={cn(
                    "flex items-center gap-2 px-2 py-3 cursor-pointer hover:bg-slate-100 transition-colors text-sm",
                    option.value === value ? "bg-slate-100 font-medium" : "",
                    index === focusedIndex ? "bg-slate-200" : ""
                  )}
                  role="option"
                  aria-selected={option.value === value}
                  tabIndex={0}
                >
                  {option.icon}
                  <span className="flex-grow">{option.label}</span>
                  {option.value === value && (
                    <Check className=&quot;ml-auto h-4 w-4 text-primary flex-shrink-0" />
                  )}
                </div>
              ))}
            </CommandGroup>
          )}
        </Command>
      </PopoverContent>
    </Popover>
  )
}