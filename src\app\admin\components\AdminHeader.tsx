'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { ThemeToggle } from '@/components/theme/theme-toggle';

export function AdminHeader() {
  const pathname = usePathname();

  const isActive = (path: string) => {
    return pathname === path || pathname.startsWith(`${path}/`);
  };

  return (
    <header className="bg-card shadow-sm border-b border-border">
      <div className=&quot;container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          <Link href=&quot;/admin" className="text-2xl font-bold text-foreground">
            Admin Dashboard
          </Link>

          <nav className=&quot;flex space-x-4">
            <Link
              href="/admin/applications"
              className={`px-4 py-2 rounded-md transition-colors ${isActive('/admin/applications')
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                }`}
            >
              Applications
            </Link>
            <Link
              href=&quot;/admin/users"
              className={`px-4 py-2 rounded-md transition-colors ${isActive('/admin/users')
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                }`}
            >
              Users
            </Link>
            <Link
              href="/admin/events"
              className={`px-4 py-2 rounded-md transition-colors ${isActive('/admin/events')
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                }`}
            >
              Events
            </Link>
            <Link
              href=&quot;/admin/analytics"
              className={`px-4 py-2 rounded-md transition-colors ${isActive('/admin/analytics')
                  ? 'bg-primary text-primary-foreground'
                  : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                }`}
            >
              Analytics
            </Link>
          </nav>

          <div className="flex items-center space-x-4">
            <ThemeToggle />
            <Link
              href=&quot;/dashboard"
              className="text-sm text-muted-foreground hover:text-foreground"
            >
              Back to Dashboard
            </Link>
          </div>
        </div>
      </div>
    </header>
  );
}