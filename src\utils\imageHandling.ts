/**
 * Utility functions for handling image loading and errors
 */

import { logger } from '@/lib/logger'

/**
 * Default fallback image to use when an image fails to load
 */
export const DEFAULT_FALLBACK_IMAGE = '/images/fallback/fallback-default.svg';

/**
 * Get a more specific fallback image based on the category
 * @param category The category of content
 * @returns Path to an appropriate fallback image
 */
export function getCategoryFallbackImage(category?: string): string {
  if (!category) return DEFAULT_FALLBACK_IMAGE;

  const lowerCategory = category.toLowerCase();

  // Map categories to specific fallback images
  const categoryMap: Record<string, string> = {
    'basketball': '/images/fallback/basketball.jpg',
    'swimming': '/images/fallback/swimming.jpg',
    'soccer': '/images/fallback/soccer.jpg',
    'football': '/images/fallback/soccer.jpg', // Alias
    'track': '/images/fallback/track.jpg',
    'tennis': '/images/fallback/tennis.jpg',
    'volleyball': '/images/fallback/volleyball.jpg',
    'chess': '/images/fallback/chess.jpg',
    'yoga': '/images/fallback/yoga.jpg',
    'trading': '/images/fallback/trading.jpg',
    'concert': '/images/fallback/concert.jpg',
    'default': DEFAULT_FALLBACK_IMAGE
  };

  return categoryMap[lowerCategory] || DEFAULT_FALLBACK_IMAGE;
}

/**
 * Create a custom Image component with built-in error handling
 * This can be used in components that need consistent image error handling
 */
export function getImageWithFallback(src: string, fallbackSrc?: string): string {
  // In a real implementation, you might check if the image exists in the cache
  // or has previously failed to load
  return src || fallbackSrc || DEFAULT_FALLBACK_IMAGE;
}

/**
 * Default avatar image to use when no avatar is available
 */
export const DEFAULT_AVATAR_IMAGE = '/images/fallback/default-avatar.svg';

/**
 * Get a user avatar URL with fallback
 * @param avatarUrl The user's avatar URL
 * @returns A valid avatar URL or fallback
 */
export function getUserAvatar(avatarUrl?: string | null): string {
  if (!avatarUrl) return DEFAULT_AVATAR_IMAGE;

  try {
    // Check if the URL is valid
    new URL(avatarUrl);

    // Check if it's a Google avatar URL that needs to be modified
    if (avatarUrl.includes('googleusercontent.com')) {
      try {
        // Make sure we're using https
        if (avatarUrl.startsWith('http:')) {
          avatarUrl = avatarUrl.replace('http:', 'https:');
        }

        // Ensure we're using lh3.googleusercontent.com format (Google's recommended CDN)
        if (!avatarUrl.includes('lh3.googleusercontent.com') && avatarUrl.includes('googleusercontent.com')) {
          const urlParts = avatarUrl.split('googleusercontent.com');
          if (urlParts.length > 1) {
            avatarUrl = `https://lh3.googleusercontent.com${urlParts[1]}`;
          }
        }

        // Remove any existing size parameters
        let cleanUrl = avatarUrl;
        if (avatarUrl.includes('=')) {
          // Remove everything after the = sign to clean existing parameters
          const urlParts = avatarUrl.split('=');
          if (urlParts.length > 0 && urlParts[0]) {
            cleanUrl = urlParts[0];
          }
        }

        // Add the size parameter using Google's recommended format
        // =s256-c for a 256px square with cropping (good for avatars)
        const finalUrl = `${cleanUrl}=s256-c`;

        if (process.env.NODE_ENV === 'development') {
          logger.debug('Using Google avatar URL:', finalUrl);
        }
        return finalUrl;
      } catch (error) {
        if (process.env.NODE_ENV === 'development') {
          console.error('Error formatting Google avatar URL:', error);
        }
        // Return the original URL if there was an error in processing
        return avatarUrl;
      }
    }

    return avatarUrl;
  } catch (e) {
    // If the URL is invalid, return the default avatar
    if (process.env.NODE_ENV === 'development') {
      console.warn('Invalid avatar URL:', avatarUrl, e);
    }

    // If it looks like a Google URL but has syntax issues, try to fix it
    if (typeof avatarUrl === 'string' && avatarUrl.includes('googleusercontent.com')) {
      if (process.env.NODE_ENV === 'development') {
        logger.info('Attempting to fix malformed Google URL');
      }

      // Ensure it has https://
      if (!avatarUrl.startsWith('http')) {
        avatarUrl = 'https://' + avatarUrl.replace(/^\/\//, '');
        if (process.env.NODE_ENV === 'development') {
          logger.info('Fixed protocol:', avatarUrl);
        }

        // Also ensure it has lh3 prefix
        if (!avatarUrl.includes('lh3.googleusercontent.com') && avatarUrl.includes('googleusercontent.com')) {
          const urlParts = avatarUrl.split('googleusercontent.com');
          if (urlParts.length > 1) {
            avatarUrl = `https://lh3.googleusercontent.com${urlParts[1]}`;
            if (process.env.NODE_ENV === 'development') {
              logger.info('Fixed to lh3 format:', avatarUrl);
            }
          }
        }

        return avatarUrl;
      }
    }

    return DEFAULT_AVATAR_IMAGE;
  }
}

/**
 * Extract avatar URL from OAuth provider metadata
 * @param userMetadata User metadata from OAuth provider
 * @returns Avatar URL or null if not available
 */
export function extractAvatarFromOAuth(userMetadata: Record<string, unknown> | null | undefined): string | null {
  if (!userMetadata) return null;

  // Only log in development mode to reduce console noise
  if (process.env.NODE_ENV === 'development') {
    logger.info('OAuth user metadata:', JSON.stringify(userMetadata));
  }

  // Prioritize Google's picture field
  if (userMetadata.picture && typeof userMetadata.picture === 'string' && userMetadata.picture.includes('googleusercontent.com')) {
    if (process.env.NODE_ENV === 'development') {
      logger.info('Found Google avatar in metadata:', userMetadata.picture);
    }

    try {
      // Parse the URL to work with it more safely
      let googleAvatarUrl = userMetadata.picture;

      // Ensure the URL is using HTTPS
      if (googleAvatarUrl.startsWith('http:')) {
        googleAvatarUrl = googleAvatarUrl.replace('http:', 'https:');
      }

      // Ensure we're using lh3.googleusercontent.com format (Google's recommended CDN)
      if (!googleAvatarUrl.includes('lh3.googleusercontent.com') && googleAvatarUrl.includes('googleusercontent.com')) {
        // Extract the path part after googleusercontent.com
        const urlParts = googleAvatarUrl.split('googleusercontent.com');
        if (urlParts.length > 1) {
          googleAvatarUrl = `https://lh3.googleusercontent.com${urlParts[1]}`;
          if (process.env.NODE_ENV === 'development') {
            logger.info('Converted to lh3 format:', googleAvatarUrl);
          }
        }
      }

      // Remove any existing size parameters
      let cleanUrl = googleAvatarUrl;
      if (googleAvatarUrl.includes('=')) {
        // Remove everything after the = sign to clean existing parameters
        const urlParts = googleAvatarUrl.split('=');
        if (urlParts.length > 0 && urlParts[0]) {
          cleanUrl = urlParts[0];
        }
      }

      // Add the size parameter using Google's recommended format
      // =s256-c for a 256px square with cropping (good for avatars)
      const finalUrl = `${cleanUrl}=s256-c`;

      if (process.env.NODE_ENV === 'development') {
        logger.info('Formatted Google avatar URL:', finalUrl);
      }

      return finalUrl;
    } catch (error) {
      if (process.env.NODE_ENV === 'development') {
        console.error('Error formatting Google avatar URL:', error);
      }
      // Return the original URL if there was an error in processing
      return userMetadata.picture;
    }
  }

  // Check for avatar in the full user object (some providers nest it)
  const userObject = userMetadata.user as Record<string, unknown> | undefined;
  if (userObject?.picture && typeof userObject.picture === 'string') {
    if (process.env.NODE_ENV === 'development') {
      logger.info('Found avatar in nested user object:', userObject.picture);
    }
    return userObject.picture;
  }

  // Check common OAuth provider avatar fields
  const avatarUrl = (typeof userMetadata.avatar_url === 'string' ? userMetadata.avatar_url : null) ||
    (typeof userMetadata.picture === 'string' ? userMetadata.picture : null) ||
    (typeof userMetadata.avatar === 'string' ? userMetadata.avatar : null) ||
    null;

  if (process.env.NODE_ENV === 'development') {
    logger.info('Using avatar URL:', avatarUrl);
  }
  return avatarUrl;
}