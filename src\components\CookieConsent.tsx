'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'

export function CookieConsent() {
  const [isOpen, setIsOpen] = useState(false)
  const [isConsentGiven, setIsConsentGiven] = useState(false)

  useEffect(() => {
    // Check if consent has been given before
    const storedConsent = localStorage.getItem('cookie-consent')
    if (storedConsent) {
      setIsConsentGiven(JSON.parse(storedConsent))
    } else {
      setIsOpen(true)
    }
  }, [])

  const acceptCookies = () => {
    localStorage.setItem('cookie-consent', 'true')
    setIsConsentGiven(true)
    setIsOpen(false)
  }

  const declineCookies = () => {
    localStorage.setItem('cookie-consent', 'false')
    setIsConsentGiven(false)
    setIsOpen(false)
  }

  if (!isOpen || isConsentGiven) return null

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-md p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
      <h3 className=&quot;text-lg font-semibold mb-2">Cookie Policy</h3>
      <p className="text-sm text-gray-600 mb-4">
        We use cookies to enhance your experience. By continuing to visit this site you agree to our use of cookies.{' '}
        <Link href=&quot;/cookie-policy" className="underline text-blue-600">
          Learn more
        </Link>
      </p>
      <div className=&quot;flex justify-end space-x-2">
        <Button variant="outline" onClick={declineCookies}>
          Decline
        </Button>
        <Button onClick={acceptCookies}>
          Accept
        </Button>
      </div>
    </div>
  )
} 