import { redirect } from 'next/navigation';
import { UserRole } from '../../../../types/roles';
import OrganizerApplicationForm from './OrganizerApplicationForm';
import { getUserApplication } from './actions';
import { createClient } from '@/lib/supabase/pages-client';

export default async function ApplicationPage() {
  const supabase = await createClient();
  const { data: { session } } = await supabase.auth.getSession();

  if (!session || !session.user) {
    redirect('/sign-in');
  }

  const userId = session.user.id;

  // If user is already an event organizer, redirect to organizations page
  const metadata = session.user.user_metadata as { role?: UserRole } || {};
  const isEventOrganizer = metadata.role === UserRole.EVENT_ORGANIZER;

  if (isEventOrganizer) {
    redirect('/dashboard/organizations');
  }

  // Load saved application if exists
  const savedApplication = await getUserApplication(userId);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className=&quot;text-3xl font-bold mb-2">Event Organizer Application</h1>
      <p className="text-gray-500 mb-6">
        Complete this application to become an event organizer on our platform.
      </p>

      <OrganizerApplicationForm
        userId={userId}
        savedData={savedApplication?.data || null}
        currentStep={savedApplication?.data?.completedSteps?.length ? savedApplication.data.completedSteps.length : 1} />
    </div>
  );
}