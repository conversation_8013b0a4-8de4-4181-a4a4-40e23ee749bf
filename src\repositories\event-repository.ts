import { BaseRepository, RepositoryResult, RepositoryOptions } from '@/lib/db/base-repository';
import { z } from 'zod';
import { createClient } from '@/lib/supabase/pages-client';
import { UserRepository } from '@/repositories/user-repository';
import { v4 as uuidv4 } from 'uuid';
import { toISOString, toDate } from '@/lib/utils/date-utils';
import { makeSlug } from '@/lib/utils';
import { logger } from '@/lib/logger';

// Image database schema
const ImageDBSchema = z.object({
  url: z.string(),
  path: z.string(),
});

// Event database schema - Must match EventDB interface exactly
const EventDBSchema = z.object({
  id: z.string().uuid(),
  title: z.string(),
  description: z.string().nullable(),
  event_type_id: z.string().uuid(),
  location: z.string(),
  state: z.string(),
  country: z.string().nullable(),
  city: z.string().nullable(), // City field may be null in database
  start_date: z.string().nullable(),
  end_date: z.string().nullable(),
  timezone: z.string(),
  organizer_id: z.string(),
  created_by: z.string(),
  status: z.enum(['draft', 'published', 'cancelled', 'completed']),
  emergency_contact_settings: z.any().optional().nullable(), // Make it optional and nullable
  total_capacity: z.number().int().positive().nullable(),
  registration_close_date: z.string().datetime().nullable(),
  allow_category_specific_closing_dates: z.boolean(),
  tshirt_options: z.any().nullable(), // JSONB field for T-shirt configuration
  created_at: z.string(),
  updated_at: z.string(),
  published_at: z.string().nullable(),
  poster_image: z.object({
    url: z.string(),
    path: z.string(),
  }).nullable(), // Poster image
  cover_image: z.object({
    url: z.string(),
    path: z.string(),
  }).nullable(), // Cover image
  slug: z.string().nullable(), // URL-friendly identifier
});

// Image schema
const imageSchema = z.object({
  url: z.string().url(),
  path: z.string(),
});

// Event application schema
export const eventSchema = z.object({
  id: z.string().uuid(),
  title: z.string(),
  description: z.string().optional(),
  eventTypeId: z.string().uuid(),
  location: z.string(),
  state: z.string(),
  country: z.string().optional(),
  city: z.string().optional(),
  // Add organizer information
  organizerName: z.string().optional(),
  contactEmail: z.string().optional(),
  contactPhone: z.string().optional(),
  organizer: z.object({
    name: z.string().optional(),
    email: z.string().optional(),
    phone: z.string().optional()
  }).optional(),
  startDate: z.union([
    z.string(),
    z.date(),
    z.null()
  ]).nullable().optional().transform(val => {
    // Just pass through, handle transformation during create/update
    return val;
  }),
  endDate: z.union([
    z.string(),
    z.date(),
    z.null()
  ]).nullable().optional().transform(val => {
    // Just pass through, handle transformation during create/update
    return val;
  }),
  timezone: z.string(),
  organizerId: z.string(),
  createdBy: z.string(),
  status: z.enum(['draft', 'published', 'cancelled', 'completed']),
  emergencyContactSettings: z.any().optional(),
  // New fields
  totalCapacity: z.number().int().positive().optional(),
  registrationCloseDate: z.union([
    z.string(),
    z.date(),
    z.null()
  ]).nullable().optional(),
  allowCategorySpecificClosingDates: z.boolean().optional().default(false),
  tshirtOptions: z.object({
    enabled: z.boolean().default(false),
    sizes: z.array(z.string()).default(["XS", "S", "M", "L", "XL", "XXL", "XXXL"]),
    description: z.string().optional(),
    sizeChartImage: z.any().optional()
  }).nullable().optional(),
  // Existing fields
  createdAt: z.date(),
  updatedAt: z.date(),
  publishedAt: z.date().optional(),
  categories: z.array(z.any()).optional(),
  customFields: z.array(z.any()).optional(),
  // Image fields
  posterImage: imageSchema.nullable().optional(),
  coverImage: imageSchema.nullable().optional(),
  galleryImages: z.array(imageSchema).nullable().optional(),
  // URL-friendly identifier
  slug: z.string().optional(),
});

// Type for the Event application model
export type Event = z.infer<typeof eventSchema>;

// Image database type
export interface ImageDB {
  url: string;
  path: string;
}

// Database event type
export interface EventDB {
  id: string;
  title: string;
  description: string | null;
  event_type_id: string;
  location: string;
  state: string;
  country: string | null;
  city: string | null; // City field may be null in database
  start_date: string | null;
  end_date: string | null;
  timezone: string;
  organizer_id: string;
  created_by: string;
  status: 'draft' | 'published' | 'cancelled' | 'completed';
  emergency_contact_settings?: unknown | null; // Make it optional to match schema
  total_capacity?: number | null; // Total capacity for the entire event
  registration_close_date?: string | null; // Default registration closing date
  allow_category_specific_closing_dates?: boolean; // Whether to allow different closing dates per category
  tshirt_options?: unknown | null; // T-shirt configuration options
  created_at: string;
  updated_at: string;
  published_at: string | null;
  poster_image?: ImageDB | null; // Poster image
  cover_image?: ImageDB | null; // Cover image
  slug?: string | null; // URL-friendly identifier
}

/**
 * Repository for event operations
 */
export class EventRepository extends BaseRepository<EventDB> {
  constructor() {
    super('events', EventDBSchema);
  }

  /**
   * Transform the database event to the application event
   */
  private toEvent(dbEvent: EventDB): Event {
    return {
      id: dbEvent.id,
      title: dbEvent.title,
      description: dbEvent.description || undefined,
      eventTypeId: dbEvent.event_type_id,
      location: dbEvent.location,
      state: dbEvent.state,
      country: dbEvent.country || undefined,
      city: dbEvent.city || undefined,
      startDate: toDate(dbEvent.start_date),
      endDate: toDate(dbEvent.end_date),
      timezone: dbEvent.timezone,
      organizerId: dbEvent.organizer_id,
      createdBy: dbEvent.created_by,
      status: dbEvent.status,
      emergencyContactSettings: dbEvent.emergency_contact_settings,
      // New fields
      totalCapacity: dbEvent.total_capacity || undefined,
      registrationCloseDate: toDate(dbEvent.registration_close_date),
      allowCategorySpecificClosingDates: dbEvent.allow_category_specific_closing_dates || false,
      tshirtOptions: dbEvent.tshirt_options || undefined,
      // Existing fields
      createdAt: new Date(dbEvent.created_at),
      updatedAt: new Date(dbEvent.updated_at),
      publishedAt: dbEvent.published_at ? new Date(dbEvent.published_at) : undefined,
      // Handle image fields
      posterImage: dbEvent.poster_image || undefined,
      coverImage: dbEvent.cover_image || undefined,
      // URL-friendly identifier
      slug: dbEvent.slug || undefined,
    };
  }

  /**
   * Transform the application event to the database event
   */
  private toDbEvent(event: Partial<Event>): Partial<EventDB> {
    // Create the basic DB event object
    const dbEvent: Partial<EventDB> = {};

    // Map all available fields
    if (event.id !== undefined) dbEvent.id = event.id;
    if (event.title !== undefined) dbEvent.title = event.title;
    if (event.description !== undefined) dbEvent.description = event.description || null;
    if (event.eventTypeId !== undefined) dbEvent.event_type_id = event.eventTypeId;
    if (event.location !== undefined) dbEvent.location = event.location;
    if (event.state !== undefined) dbEvent.state = event.state;
    if (event.country !== undefined) dbEvent.country = event.country || null;
    if (event.city !== undefined) dbEvent.city = event.city || null;
    if (event.startDate !== undefined) dbEvent.start_date = toISOString(event.startDate);
    if (event.endDate !== undefined) dbEvent.end_date = toISOString(event.endDate);
    if (event.timezone !== undefined) dbEvent.timezone = event.timezone;
    if (event.organizerId !== undefined) dbEvent.organizer_id = event.organizerId;
    if (event.createdBy !== undefined) dbEvent.created_by = event.createdBy;
    if (event.status !== undefined) dbEvent.status = event.status;
    if (event.emergencyContactSettings !== undefined) dbEvent.emergency_contact_settings = event.emergencyContactSettings || null;

    // New fields
    if (event.totalCapacity !== undefined) dbEvent.total_capacity = event.totalCapacity;
    if (event.registrationCloseDate !== undefined) dbEvent.registration_close_date = toISOString(event.registrationCloseDate);
    if (event.allowCategorySpecificClosingDates !== undefined) dbEvent.allow_category_specific_closing_dates = event.allowCategorySpecificClosingDates;
    if (event.tshirtOptions !== undefined) dbEvent.tshirt_options = event.tshirtOptions;

    // Handle dates specifically
    if (event.createdAt !== undefined) dbEvent.created_at = event.createdAt.toISOString();
    if (event.updatedAt !== undefined) dbEvent.updated_at = event.updatedAt.toISOString();
    if (event.publishedAt !== undefined) dbEvent.published_at = event.publishedAt ? event.publishedAt.toISOString() : null;

    // Handle image fields
    if (event.posterImage !== undefined) dbEvent.poster_image = event.posterImage || null;
    if (event.coverImage !== undefined) dbEvent.cover_image = event.coverImage || null;

    // Handle slug field
    if (event.slug !== undefined) dbEvent.slug = event.slug || null;

    return dbEvent;
  }

  /**
   * Get all events
   */
  async getAllEvents(): Promise<Event[]> {
    const result = await this.find();
    if (!result.success) {
      throw new Error(`Failed to get events: ${result.message}`);
    }
    return (result.data || []).map(this.toEvent);
  }

  /**
   * Get event by ID with application model conversion
   */
  async getEventById(id: string, options?: RepositoryOptions): Promise<Event | null> {
    if (process.env.NODE_ENV === 'development') {

      logger.debug(`[DEBUG] EventRepository.getEventById - Starting to fetch event with ID: ${id}`);

    }
    // Use the base repository findById method
    const result = await super.findById(id, options);

    if (process.env.NODE_ENV === 'development') {


      logger.debug(`[DEBUG] EventRepository.getEventById - Result for ID ${id}:`, {
      success: result.success,
      hasData: !!result.data,
      error: result.error?.message,
      message: result.message
    });


    }
    if (!result.success || !result.data) {
      console.error(`[DEBUG] EventRepository.getEventById - Failed to find event with ID: ${id}`, {
        error: result.error?.message,
        message: result.message
      });
      return null;
    }

    if (process.env.NODE_ENV === 'development') {


      logger.debug(`[DEBUG] EventRepository.getEventById - Found event with ID: ${id}, title: ${result.data.title}`);


    }
    // Convert to application model
    const event = this.toEvent(result.data);

    if (process.env.NODE_ENV === 'development') {


      logger.debug(`[DEBUG] EventRepository.getEventById - Converted event with ID: ${id}`, {
      title: event.title,
      status: event.status,
      hasCategories: !!event.categories,
      hasCustomFields: !!event.customFields
    });


    }
    return event;
  }

  /**
   * Get event by slug
   */
  async getEventBySlug(slug: string): Promise<Event | null> {
    try {
      const result = await this.find({ slug });

      if (!result.success || !result.data || result.data.length === 0) {
        return null;
      }

      if (result.data && result.data.length > 0 && result.data[0]) {
        return this.toEvent(result.data[0]);
      }
      return null;
    } catch (error) {
      console.error(`Error getting event by slug: ${error}`);
      return null;
    }
  }

  /**
   * Override findById but keep the original in BaseRepository accessible via getEventById
   * @deprecated Use getEventById instead for application model conversion
   */
  public override async findById(id: string, options?: RepositoryOptions): Promise<RepositoryResult<EventDB>> {
    return super.findById(id, options);
  }

  /**
   * Get events by organizer ID
   * Works with either user IDs (starting with "user_") or internal UUIDs
   */
  async getEventsByOrganizerId(organizerId: string): Promise<Event[]> {
    if (process.env.NODE_ENV === 'development') {

      logger.debug(`[DEBUG] getEventsByOrganizerId called with organizerId: ${organizerId}`);

    }
    // Check if this is a user ID that starts with "user_" (from Supabase Auth)
    if (organizerId.startsWith("user_")) {
      if (process.env.NODE_ENV === 'development') {

        logger.debug(`[DEBUG] Detected user ID: ${organizerId}, checking if it's already in the database`);

      }
      // First, check if this is already an internal user ID in the users table
      try {
        // Try to find events directly by organizer_id first
        const directResult = await this.find({ organizer_id: organizerId });
        if (directResult.success && directResult.data && directResult.data.length > 0) {
          if (process.env.NODE_ENV === 'development') {

            logger.debug(`[DEBUG] Found events directly with organizer_id: ${organizerId}, count: ${directResult.data.length}`);

          }
          return directResult.data.map(this.toEvent);
        }

        // If no events found directly, try to find the internal user ID
        const supabase = await createClient();
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('id')
          .eq('auth_user_id', organizerId)
          .maybeSingle();

        if (userError) {
          console.error(`[DEBUG] Error looking up user by auth_user_id: ${userError.message}`);
        }

        if (userData) {
          if (process.env.NODE_ENV === 'development') {

            logger.debug(`[DEBUG] Found internal user ID: ${userData.id} for auth_user_id: ${organizerId}`);

          }
          // Use the internal ID to query events
          const result = await this.find({ organizer_id: userData.id });
          if (process.env.NODE_ENV === 'development') {

            logger.debug(`[DEBUG] Events query result: success=${result.success}, count=${result.data?.length || 0}`);

          }
          if (!result.success) {
            console.error(`[DEBUG] Failed to get events: ${result.message}`);
            throw new Error(`Failed to get events: ${result.message}`);
          }
          return (result.data || []).map(this.toEvent);
        }

        // Check if this ID exists in the users table directly
        const { data: directUserData, error: directUserError } = await supabase
          .from('users')
          .select('id')
          .eq('id', organizerId)
          .single();

        if (directUserData) {
          if (process.env.NODE_ENV === 'development') {

            logger.debug(`[DEBUG] Found user with ID: ${organizerId} directly in users table`);

          }
          // This is a valid internal user ID, query events directly
          const result = await this.find({ organizer_id: organizerId });
          if (process.env.NODE_ENV === 'development') {

            logger.debug(`[DEBUG] Events query result for internal user: success=${result.success}, count=${result.data?.length || 0}`);

          }
          return (result.data || []).map(this.toEvent);
        }

        // If we get here, we couldn't find a matching user
        console.warn(`[DEBUG] No user found with ID: ${organizerId}`);
        return [];
      } catch (error) {
        console.error(`[DEBUG] Error finding user:`, error);
        return [];
      }
    } else {
      if (process.env.NODE_ENV === 'development') {

        logger.debug(`[DEBUG] Using direct UUID format ID: ${organizerId}`);

      }
      // For UUID format IDs (admins or direct database queries)
      const result = await this.find({ organizer_id: organizerId });
      if (process.env.NODE_ENV === 'development') {

        logger.debug(`[DEBUG] Direct UUID query result: success=${result.success}, count=${result.data?.length || 0}`);

      }
      if (!result.success) {
        console.error(`[DEBUG] Failed to get events with UUID: ${result.message}`);
        throw new Error(`Failed to get events: ${result.message}`);
      }
      return (result.data || []).map(this.toEvent);
    }
  }

  /**
   * Get count of events by organizer ID
   * Works with either Supabase Auth IDs (starting with "user_") or internal UUIDs
   */
  async countEventsByOrganizerId(organizerId: string): Promise<number> {
    // Get the events using our existing method
    const events = await this.getEventsByOrganizerId(organizerId);
    return events.length;
  }

  /**
   * Get the count of events by status and organizer ID
   */
  async getEventCountsByStatus(organizerId: string): Promise<Record<string, number>> {
    // Get all events for this organizer
    const events = await this.getEventsByOrganizerId(organizerId);

    // Initialize counters
    const draftCount = 0;
    const publishedCount = 0;
    const cancelledCount = 0;
    const completedCount = 0;
    // Tally up statuses
    events.forEach(event => {
      // Make sure event and status exist and are valid
      if (event && event.status) {
        // Type guard to ensure status is a valid key
        const status = event.status;
        if (status === 'draft') {
          draftCount++;
        } else if (status === 'published') {
          publishedCount++;
        } else if (status === 'cancelled') {
          cancelledCount++;
        } else if (status === 'completed') {
          completedCount++;
        }
      }
    });

    // Return the counts as a record
    return {
      draft: draftCount,
      published: publishedCount,
      cancelled: cancelledCount,
      completed: completedCount,
      total: events.length
    };
  }

  /**
   * Create a new event
   */
  public async createEvent(event: Partial<Event>): Promise<RepositoryResult<Event | null>> {
    try {
      // Validate using partial schema to allow flexibility
      const validationResult = eventSchema.partial().safeParse(event);
      if (!validationResult.success) {
        console.error('Event validation failed:', validationResult.error);
        return {
          success: false,
          error: validationResult.error,
          message: `Event validation failed: ${validationResult.error}`
        };
      }

      // Ensure required fields have defaults
      if (!event.createdBy) {
        return {
          success: false,
          error: new Error('created_by field is required when creating an event'),
          message: 'created_by field is required when creating an event'
        };
      }

      const now = new Date().toISOString();

      // Generate a slug from the title if not provided
      const slug = event.slug || (event.title ? makeSlug(event.title) : null);

      // Prepare database event with proper date handling
      const newEventData: EventDB = {
        id: event.id || uuidv4(),
        title: event.title || '',
        description: event.description || null,
        event_type_id: event.eventTypeId || '',
        location: event.location || '',
        state: event.state || '',
        country: event.country || null,
        city: event.city || null,
        start_date: toISOString(event.startDate),
        end_date: toISOString(event.endDate),
        timezone: event.timezone || '',
        organizer_id: event.organizerId || '',
        created_by: event.createdBy,
        status: event.status || 'draft',
        emergency_contact_settings: event.emergencyContactSettings || null,
        // New fields
        total_capacity: event.totalCapacity || null,
        registration_close_date: toISOString(event.registrationCloseDate),
        allow_category_specific_closing_dates: event.allowCategorySpecificClosingDates || false,
        tshirt_options: event.tshirtOptions || {
          enabled: false,
          sizes: ["XS", "S", "M", "L", "XL", "XXL", "XXXL"],
          description: "",
          sizeChartImage: null
        },
        // Existing fields
        created_at: now,
        updated_at: now,
        published_at: null,
        // Add image fields
        poster_image: event.posterImage || null,
        cover_image: event.coverImage || null,
        // Add slug
        slug
      };

      const result = await this.create(newEventData);

      if (!result.success) {
        return {
          success: false,
          error: result.error,
          message: `Failed to create event: ${result.message}`
        };
      }

      return {
        success: true,
        data: result.data ? this.toEvent(result.data) : null
      };
    } catch (error) {
      console.error('Error in createEvent:', error);
      return {
        success: false,
        error,
        message: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Update an event
   */
  async updateEvent(id: string, event: Partial<Omit<Event, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Event> {
    // Create a partial database event object using our utility function
    const dbEvent = this.toDbEvent(event);

    // Always update the updated_at timestamp
    dbEvent.updated_at = new Date().toISOString();

    // If title is being updated, regenerate the slug (unless slug is explicitly provided)
    if (event.title && !event.slug) {
      dbEvent.slug = makeSlug(event.title);
    }

    const result = await this.update(id, dbEvent);

    if (!result.success) {
      throw new Error(`Failed to update event: ${result.message}`);
    }

    return this.toEvent(result.data!);
  }

  /**
   * Publish an event
   */
  async publishEvent(id: string): Promise<Event> {
    const now = new Date().toISOString();

    const result = await this.update(id, {
      status: 'published',
      published_at: now,
      updated_at: now
    });

    if (!result.success) {
      throw new Error(`Failed to publish event: ${result.message}`);
    }

    return this.toEvent(result.data!);
  }

  /**
   * Delete an event
   */
  async deleteEvent(id: string): Promise<void> {
    const result = await this.delete(id);

    if (!result.success) {
      throw new Error(`Failed to delete event: ${result.message}`);
    }
  }
}