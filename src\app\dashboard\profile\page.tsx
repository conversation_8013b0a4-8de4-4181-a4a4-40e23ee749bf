import { createClient } from '@/lib/supabase/server';
import { redirectToSignIn, redirectToDashboard } from '@/lib/redirect-actions';
import ProfileClient from "./profile-client";
import { getAuthUser } from '@/lib/auth-utils';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { logger } from '@/lib/logger';

export default async function ProfilePage() {
  if (process.env.NODE_ENV === 'development') {

    logger.debug('[DEBUG] ProfilePage - Initializing');

  }
  try {
    // Check if we have Supabase auth cookies - this is a quick check before doing any DB calls
    const cookieStore = await cookies();
    const hasAuthCookies = cookieStore.getAll().some(cookie =>
      cookie.name.includes('auth-token') || cookie.name.includes('supabase-auth')
    );

    if (process.env.NODE_ENV === 'development') {


      logger.debug('[DEBUG] ProfilePage - Has auth cookies:', hasAuthCookies);


    }
    if (!hasAuthCookies) {
      if (process.env.NODE_ENV === 'development') {

        logger.debug('[DEBUG] ProfilePage - No auth cookies found, redirecting to sign-in');

      }
      // If no auth cookies, redirect to sign-in
      redirect('/sign-in');
    }

    // First check if we have a user from the auth context
    // This should be available if middleware has already verified authentication
    const authUser = await getAuthUser();

    // Create Supabase client
    const supabase = await createClient();

    // If no auth user from cache, try to get from session
    let userId: string;

    if (!authUser) {
      if (process.env.NODE_ENV === 'development') {

        logger.debug('[DEBUG] ProfilePage - No auth user found, checking session');

      }
      // Double-check with session as fallback
      const { data: { session }, error } = await supabase.auth.getSession();

      if (error) {
        console.error('[DEBUG] ProfilePage - Error getting session:', error);
      }

      if (!session || !session.user) {
        if (process.env.NODE_ENV === 'development') {

          logger.debug('[DEBUG] ProfilePage - No session found, but has auth cookies. Using fallback UI');

        }
        // We have auth cookies but no session - this is likely a race condition
        // Instead of redirecting, show a loading state
        return (
          <div className="container mx-auto px-4 py-8">
            <h1 className=&quot;text-3xl font-bold mb-2">Your Profile</h1>
            <p className="text-[hsl(var(--muted-foreground))] mb-6">Loading your profile information...</p>
            <div className=&quot;animate-pulse bg-[hsl(var(--muted))] dark:bg-[hsl(var(--dark-muted))] h-32 rounded-lg"></div>
          </div>
        );
      }

      userId = session.user.id;
      if (process.env.NODE_ENV === 'development') {

        logger.debug('[DEBUG] ProfilePage - Got user ID from session:', userId);

      }
    } else {
      userId = authUser.id;
      if (process.env.NODE_ENV === 'development') {

        logger.debug('[DEBUG] ProfilePage - Got user ID from auth context:', userId);

      }
    }

    // Get user data from the database
    if (process.env.NODE_ENV === 'development') {

      logger.debug('[DEBUG] ProfilePage - Fetching user data for auth_user_id:', userId);

    }
    let userData;

    // First try to find user by auth_user_id
    const { data: userByAuthId, error: authIdError } = await supabase
      .from('users')
      .select('*')
      .eq('auth_user_id', userId)
      .single();

    if (authIdError || !userByAuthId) {
      console.error('[DEBUG] ProfilePage - Error fetching user data by auth_user_id:', authIdError);

      // Try to find user by email as fallback
      const { data: { user: authUserDetails } } = await supabase.auth.getUser();

      if (authUserDetails?.email) {
        if (process.env.NODE_ENV === 'development') {

          logger.debug('[DEBUG] ProfilePage - Trying to find user by email:', authUserDetails.email);

        }
        const { data: userByEmail, error: emailError } = await supabase
          .from('users')
          .select('*')
          .eq('email', authUserDetails.email)
          .single();

        if (emailError || !userByEmail) {
          console.error('[DEBUG] ProfilePage - Error fetching user by email:', emailError);
          return (
            <div className="container mx-auto px-4 py-8">
              <h1 className=&quot;text-3xl font-bold mb-2">Profile Error</h1>
              <p className="text-[hsl(var(--muted-foreground))] mb-6">
                We couldn&apos;t find your profile information. Please try refreshing the page or contact support.
              </p>
              <button
                onClick={() => window.location.reload()}
                className=&quot;px-4 py-2 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] rounded-md"
              >
                Refresh Page
              </button>
            </div>
          );
        }

        // Use the user found by email
        userData = userByEmail;

        // Update the auth_user_id for future queries
        if (process.env.NODE_ENV === 'development') {

          logger.debug('[DEBUG] ProfilePage - Updating auth_user_id for user:', userData.id);

        }
        const { error: updateError } = await supabase
          .from('users')
          .update({ auth_user_id: userId })
          .eq('id', userData.id);

        if (updateError) {
          console.error('[DEBUG] ProfilePage - Error updating auth_user_id:', updateError);
        }
      } else {
        // No email available, show error
        return (
          <div className="container mx-auto px-4 py-8">
            <h1 className=&quot;text-3xl font-bold mb-2">Profile Error</h1>
            <p className="text-[hsl(var(--muted-foreground))] mb-6">
              We couldn&apos;t find your profile information. Please try refreshing the page or contact support.
            </p>
            <button
              onClick={() => window.location.reload()}
              className=&quot;px-4 py-2 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] rounded-md"
            >
              Refresh Page
            </button>
          </div>
        );
      }
    } else {
      // Use the user found by auth_user_id
      userData = userByAuthId;
    }

    if (!userData) {
      console.error('[DEBUG] ProfilePage - No user data found');
      return (
        <div className="container mx-auto px-4 py-8">
          <h1 className=&quot;text-3xl font-bold mb-2">Profile Error</h1>
          <p className="text-[hsl(var(--muted-foreground))] mb-6">
            We couldn&apos;t find your profile information. Please try refreshing the page or contact support.
          </p>
          <button
            onClick={() => window.location.reload()}
            className=&quot;px-4 py-2 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] rounded-md"
          >
            Refresh Page
          </button>
        </div>
      );
    }

    if (process.env.NODE_ENV === 'development') {


      logger.debug('[DEBUG] ProfilePage - User data found:', userData.id);


    }
    // Use type assertion to handle potential null values
    const userDataForClient = {
      id: userData.id,
      first_name: userData.first_name || '',
      last_name: userData.last_name || '',
      email: userData.email || '',
      role: userData.role || 'user',
      gender: userData.gender as string || '',
      username: userData.username || ''
    };

    return <ProfileClient user={userDataForClient} />;
  } catch (error) {
    // Handle any errors that occur during page rendering
    console.error('[DEBUG] ProfilePage - Error rendering page:', error);

    // Return a fallback UI
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className=&quot;text-3xl font-bold mb-2">Profile Error</h1>
        <p className="text-[hsl(var(--muted-foreground))] mb-6">
          An error occurred while loading your profile. Please try refreshing the page.
        </p>
        <button
          onClick={() => window.location.reload()}
          className=&quot;px-4 py-2 bg-[hsl(var(--primary))] text-[hsl(var(--primary-foreground))] rounded-md"
        >
          Refresh Page
        </button>
      </div>
    );
  }
}