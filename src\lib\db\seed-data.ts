'use server';

import { EventTypeRepository } from '@/repositories/event-type-repository';
import { EventField, EventType, FieldType } from '@/types/event-types';
import { logger } from '@/lib/logger';

/**
 * Seed default event types into the database
 */
export async function seedEventTypes() {
  try {
    const eventTypeRepository = new EventTypeRepository();
    
    // Check if event types already exist
    const existingTypes = await eventTypeRepository.getAllEventTypes();
    if (existingTypes.length > 0) {
      logger.info('Event types already exist, skipping seed');
      return { success: true, count: existingTypes.length };
    }
    
    // Default event types to seed
    const defaultEventTypes: Omit<EventType, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        name: 'Conference',
        slug: 'conference',
        description: 'A large, typically multi-day event for sharing knowledge and networking',
        icon: '🎤',
        baseFields: {
          requiresTshirtSize: false,
          requiresBibNumber: false,
          hasCategories: true,
          requiresAttendanceCertificate: true,
          requiresSeating: true,
          requiresAgeVerification: false,
          requiresMerchandise: false,
          requiresRanking: false,
          requiresCustomFields: true,
        },
        customFields: [
          {
            id: 'company',
            type: FieldType.TEXT,
            label: 'Company/Organization',
            description: 'Your company or organization name',
            required: true,
            order: 1
          } as EventField,
          {
            id: 'dietary',
            type: FieldType.SELECT,
            label: 'Dietary Restrictions',
            description: 'Any dietary restrictions we should know about',
            options: ['None', 'Vegetarian', 'Vegan', 'Gluten-Free', 'Dairy-Free', 'Kosher', 'Halal'],
            required: false,
            order: 2
          } as EventField
        ]
      },
      {
        name: 'Workshop',
        slug: 'workshop',
        description: 'A focused, hands-on session for learning specific skills',
        icon: '🔧',
        baseFields: {
          requiresTshirtSize: false,
          requiresBibNumber: false,
          hasCategories: false,
          requiresAttendanceCertificate: true,
          requiresSeating: true,
          requiresAgeVerification: false,
          requiresMerchandise: false,
          requiresRanking: false,
          requiresCustomFields: true,
        },
        customFields: [
          {
            id: 'experience',
            type: FieldType.SELECT,
            label: 'Experience Level',
            description: 'Your experience level with this topic',
            options: ['Beginner', 'Intermediate', 'Advanced', 'Expert'],
            required: true,
            order: 1
          } as EventField
        ]
      },
      {
        name: 'Running Event',
        slug: 'runs',
        description: 'Marathons, half-marathons, 5Ks and fun runs',
        icon: '🏃',
        baseFields: {
          requiresTshirtSize: true,
          requiresBibNumber: true,
          hasCategories: true,
          requiresAttendanceCertificate: false,
          requiresSeating: false,
          requiresAgeVerification: true,
          requiresMerchandise: true,
          requiresRanking: true,
          requiresCustomFields: true,
        },
        customFields: [
          {
            id: 'emergencyContact',
            type: FieldType.TEXT,
            label: 'Emergency Contact Name',
            description: 'Who to contact in case of emergency',
            required: true,
            order: 1
          } as EventField,
          {
            id: 'emergencyPhone',
            type: FieldType.PHONE,
            label: 'Emergency Contact Phone',
            description: 'Emergency contact phone number',
            required: true,
            order: 2
          } as EventField,
          {
            id: 'medical',
            type: FieldType.TEXT,
            label: 'Medical Conditions',
            description: 'Any medical conditions we should be aware of',
            required: false,
            order: 3
          } as EventField
        ]
      },
      {
        name: 'Webinar',
        slug: 'webinar',
        description: 'Online presentations or workshops delivered virtually',
        icon: '💻',
        baseFields: {
          requiresTshirtSize: false,
          requiresBibNumber: false,
          hasCategories: false,
          requiresAttendanceCertificate: true,
          requiresSeating: false,
          requiresAgeVerification: false,
          requiresMerchandise: false,
          requiresRanking: false,
          requiresCustomFields: true,
        },
        customFields: [
          {
            id: 'company',
            type: FieldType.TEXT,
            label: 'Company/Organization',
            description: 'Your company or organization name',
            required: false,
            order: 1
          } as EventField
        ]
      }
    ];
    
    // Create each event type
    for (const eventType of defaultEventTypes) {
      await eventTypeRepository.createEventType(eventType);
    }
    
    return { success: true, count: defaultEventTypes.length };
  } catch (error) {
    console.error('Error seeding event types:', error);
    return { success: false, error instanceof Error ? error.message : 'Unknown error' };
  }
} 