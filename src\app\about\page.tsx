import Image from 'next/image'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'About Us | Fuiyoo',
  description: 'Learn about the mission, vision, and team behind Fuiyoo'
}

export default function AboutPage() {
  return (
    <div className="container mx-auto px-4 py-16 md:py-24">
      {/* Hero Section */}
      <section className=&quot;mb-16 md:mb-24">
        <div className="text-center max-w-3xl mx-auto">
          <h1 className=&quot;text-4xl md:text-5xl font-bold mb-6">About Fuiyoo</h1>
          <p className="text-xl text-gray-600 mb-8">
            Redefining how communities experience events, one moment at a time.
          </p>
        </div>
        <div className="relative h-80 md:h-96 lg:h-[500px] rounded-2xl overflow-hidden mt-12">
          <Image
            src="https://images.pexels.com/photos/2608517/pexels-photo-2608517.jpeg"
            alt="Fuiyoo team collaborating"
            fill
            className="object-cover"
            priority />
        </div>
      </section>

      {/* Our Story */}
      <section className="mb-16 md:mb-24 max-w-4xl mx-auto">
        <h2 className=&quot;text-3xl md:text-4xl font-bold mb-8 text-center">Our Story</h2>
        <div className="prose prose-lg mx-auto">
          <p className=&quot;mb-4">
            Founded in 2023, Fuiyoo was born from a simple observation: event management was unnecessarily complex,
            and event experiences were often fragmented. Our founders, passionate about community gatherings and
            technology, set out to change that.
          </p>
          <p className="mb-4">
            The name &ldquo;Fuiyoo&rdquo; comes from a popular Malaysian expression of excitement and approval, embodying the
            joyful experience we aim to create for event organizers and attendees alike.
          </p>
          <p>
            Since our inception, we&apos;ve been on a mission to simplify event management while creating more
            memorable and sustainable event experiences. We believe that technology should enhance human
            connections, not replace them.
          </p>
        </div>
      </section>

      {/* Mission & Values */}
      <section className=&quot;mb-16 md:mb-24 bg-gray-50 py-16 -mx-4 px-4">
        <div className="max-w-4xl mx-auto">
          <h2 className=&quot;text-3xl md:text-4xl font-bold mb-12 text-center">Our Mission & Values</h2>

          <div className="grid md:grid-cols-2 gap-8">
            <div className=&quot;bg-white p-8 rounded-xl shadow-sm">
              <h3 className="text-xl font-semibold mb-4 text-primary">Our Mission</h3>
              <p className=&quot;text-gray-700">
                To create a seamless platform that brings communities together through meaningful events, while
                minimizing environmental impact and maximizing social connection.
              </p>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-sm">
              <h3 className=&quot;text-xl font-semibold mb-4 text-primary">Our Vision</h3>
              <p className="text-gray-700">
                A world where every event, big or small, leaves a positive legacy for communities and the planet.
              </p>
            </div>
          </div>

          <div className=&quot;mt-12 grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-xl shadow-sm">
              <h3 className=&quot;font-semibold mb-2 text-primary">Community Focus</h3>
              <p className="text-sm text-gray-600">We prioritize strengthening community bonds through memorable shared experiences.</p>
            </div>

            <div className=&quot;bg-white p-6 rounded-xl shadow-sm">
              <h3 className="font-semibold mb-2 text-primary">Sustainability</h3>
              <p className=&quot;text-sm text-gray-600">We&apos;re committed to reducing the environmental footprint of events through mindful design.</p>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm">
              <h3 className=&quot;font-semibold mb-2 text-primary">Innovation</h3>
              <p className="text-sm text-gray-600">We continuously evolve our platform to meet the changing needs of event organizers and attendees.</p>
            </div>

            <div className=&quot;bg-white p-6 rounded-xl shadow-sm">
              <h3 className="font-semibold mb-2 text-primary">Inclusivity</h3>
              <p className=&quot;text-sm text-gray-600">We design for accessibility, ensuring events can be enjoyed by everyone.</p>
            </div>

            <div className="bg-white p-6 rounded-xl shadow-sm">
              <h3 className=&quot;font-semibold mb-2 text-primary">Transparency</h3>
              <p className="text-sm text-gray-600">We believe in honest communication with all stakeholders in the event ecosystem.</p>
            </div>

            <div className=&quot;bg-white p-6 rounded-xl shadow-sm">
              <h3 className="font-semibold mb-2 text-primary">Excellence</h3>
              <p className=&quot;text-sm text-gray-600">We strive for excellence in every aspect of our service, from user experience to customer support.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="mb-16 md:mb-24 max-w-4xl mx-auto">
        <h2 className=&quot;text-3xl md:text-4xl font-bold mb-12 text-center">Meet Our Team</h2>

        <div className="grid md:grid-cols-3 gap-8">
          <div className=&quot;text-center">
            <div className="rounded-full overflow-hidden h-48 w-48 mx-auto mb-4 relative">
              <Image
                src=&quot;https://images.pexels.com/photos/2379005/pexels-photo-2379005.jpeg"
                alt="Sarah Chen"
                fill
                className=&quot;object-cover" />
            </div>
            <h3 className="text-xl font-semibold">Sarah Chen</h3>
            <p className=&quot;text-primary">CEO & Co-Founder</p>
            <p className="text-sm text-gray-600 mt-2">Event planning expert with a passion for community building</p>
          </div>

          <div className=&quot;text-center">
            <div className="rounded-full overflow-hidden h-48 w-48 mx-auto mb-4 relative">
              <Image
                src=&quot;https://images.pexels.com/photos/3778680/pexels-photo-3778680.jpeg"
                alt="Michael Lee"
                fill
                className=&quot;object-cover" />
            </div>
            <h3 className="text-xl font-semibold">Michael Lee</h3>
            <p className=&quot;text-primary">CTO & Co-Founder</p>
            <p className="text-sm text-gray-600 mt-2">Full-stack developer with expertise in scalable platforms</p>
          </div>

          <div className=&quot;text-center">
            <div className="rounded-full overflow-hidden h-48 w-48 mx-auto mb-4 relative">
              <Image
                src=&quot;https://images.pexels.com/photos/3771089/pexels-photo-3771089.jpeg"
                alt="Anita Kumar"
                fill
                className=&quot;object-cover" />
            </div>
            <h3 className="text-xl font-semibold">Anita Kumar</h3>
            <p className=&quot;text-primary">Head of Design</p>
            <p className="text-sm text-gray-600 mt-2">UX specialist focused on creating intuitive user experiences</p>
          </div>
        </div>
      </section>

      {/* Join Us */}
      <section className=&quot;text-center max-w-3xl mx-auto">
        <h2 className="text-3xl font-bold mb-6">Join Our Journey</h2>
        <p className=&quot;text-lg text-gray-600 mb-8">
          We&apos;re just getting started, and we&apos;d love for you to be part of our story.
          Whether you&apos;re an event organizer, attendee, or someone who shares our values,
          there&apos;s a place for you in the Fuiyoo community.
        </p>
        <div className="flex flex-wrap justify-center gap-4">
          <a href=&quot;/careers" className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2">
            Join Our Team
          </a>
          <a href=&quot;/contact" className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
            Contact Us
          </a>
        </div>
      </section>
    </div>
  )
}
