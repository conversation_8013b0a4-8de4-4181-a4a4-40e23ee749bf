import React from 'react';
import Link from 'next/link';
import { notFound } from 'next/navigation';
import { ArrowLeft, Check, Calendar, Tag } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { createClient } from '@/lib/supabase/server';
import { formatDate } from '@/utils/formatDate';

// Define the props type for the page component
type RegistrationSuccessProps = {
  params: Promise<Record<string, string>>;
  searchParams: Promise<{
    registrationId?: string;
  }>;
}

export default async function RegistrationSuccessPage({ searchParams }: RegistrationSuccessProps) {
  const { registrationId } = await searchParams;

  if (!registrationId) {
    notFound();
  }

  // Get Supabase client
  const supabase = await createClient();

  // Get the registration details
  const { data: registration } = await supabase
    .from('registrations')
    .select(`
      *,
      events:event_id (
        id,
        title,
        slug,
        start_date,
        location
      ),
      categories:category_id (
        id,
        name,
        properties
      )
    `)
    .eq('id', registrationId)
    .single();

  if (!registration) {
    notFound();
  }

  // Parse the form data from the registration
  const formData = registration.form_data ?
    (typeof registration.form_data === 'string' ?
      JSON.parse(registration.form_data) :
      registration.form_data) :
    {};

  // Get event and category data with type assertions to handle potential errors
  const event = registration.events && typeof registration.events === 'object' ?
    registration.events as {
      title?: string;
      slug?: string;
      start_date?: string;
    } :
    {};

  const category = registration.categories && typeof registration.categories === 'object' ?
    registration.categories as {
      name?: string;
    } :
    {};

  return (
    <div className="container max-w-4xl mx-auto px-4 py-12">
      <div className=&quot;mb-8 text-center">
        <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-100 dark:bg-green-900 mb-4">
          <Check className=&quot;w-8 h-8 text-green-600 dark:text-green-400" />
        </div>
        <h1 className="text-3xl font-bold mb-2">Registration Successful!</h1>
        <p className=&quot;text-muted-foreground">
          Your registration for {event?.title || 'this event'} has been confirmed.
        </p>
      </div>

      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Registration Details</CardTitle>
          <CardDescription>
            Your registration has been confirmed with the following details
          </CardDescription>
        </CardHeader>
        <CardContent className=&quot;space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className=&quot;space-y-4">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Event</h3>
                <p className=&quot;text-lg font-medium">{event?.title || 'Event'}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Category</h3>
                <div className=&quot;flex items-center">
                  <Tag className="w-4 h-4 text-indigo-600 dark:text-indigo-400 mr-2" />
                  <span>{category?.name || 'General'}</span>
                </div>
              </div>

              <div>
                <h3 className=&quot;text-sm font-medium text-muted-foreground">Date</h3>
                <div className="flex items-center">
                  <Calendar className=&quot;w-4 h-4 text-indigo-600 dark:text-indigo-400 mr-2" />
                  <span>{event?.start_date ? formatDate(event.start_date) : 'TBA'}</span>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <h3 className=&quot;text-sm font-medium text-muted-foreground">Registrant</h3>
                <p className="text-lg font-medium">{formData.first_name || ''} {formData.last_name || ''}</p>
              </div>

              <div>
                <h3 className=&quot;text-sm font-medium text-muted-foreground">Email</h3>
                <p>{formData.email || 'Not provided'}</p>
              </div>

              <div>
                <h3 className="text-sm font-medium text-muted-foreground">Phone</h3>
                <p>{formData.phone || 'Not provided'}</p>
              </div>

              {formData.tshirt_size && (
                <div>
                  <h3 className=&quot;text-sm font-medium text-muted-foreground">T-Shirt Size</h3>
                  <p>{formData.tshirt_size}</p>
                </div>
              )}
            </div>
          </div>

          <div className="bg-blue-50 dark:bg-blue-950/30 p-4 rounded-md border border-blue-100 dark:border-blue-900">
            <p className=&quot;text-sm text-blue-700 dark:text-blue-300">
              A confirmation email has been sent to your registered email address with these details.
            </p>
          </div>
        </CardContent>
      </Card>

      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button variant=&quot;outline" size="lg" asChild/>
          <Link href={`/events/${event?.slug || ''}`}>
            <ArrowLeft className=&quot;w-4 h-4 mr-2" />
            Back to Event
          </Link>
        </Button>

        <Button size="lg" className=&quot;bg-indigo-600 hover:bg-indigo-700 text-white" asChild>
          <Link href="/dashboard/registrations">
            View My Registrations
          </Link>
        </Button>
      </div>
    </div>
  );
}
