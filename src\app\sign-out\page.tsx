'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function SignOutPage() {
  const router = useRouter()

  useEffect(() => {
    // Immediately redirect to home page
    // The actual sign-out logic is now handled in the auth.ts file
    router.push('/')
  }, [router])

  return (
    <div className="flex min-h-screen flex-col items-center justify-center">
      <div className=&quot;w-full max-w-md space-y-8 p-8">
        <div className="text-center">
          <h2 className=&quot;mt-6 text-3xl font-bold tracking-tight text-foreground">
            Redirecting...
          </h2>
          <p className="mt-2 text-sm text-muted-foreground">
            You are being redirected to the home page.
          </p>
        </div>
      </div>
    </div>
  )
}
