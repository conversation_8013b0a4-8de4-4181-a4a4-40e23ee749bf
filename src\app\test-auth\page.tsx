import { createClient } from '@/lib/supabase/server';

export default async function TestAuthPage() {
  // Get the Supabase client using the server-side implementation
  const supabase = await createClient();

  // Get the authenticated user
  const { data: { user }, error: userError } = await supabase.auth.getUser();

  // Get the session
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();

  return (
    <div className="p-8">
      <h1 className=&quot;text-2xl font-bold mb-4">Authentication Test Page</h1>

      <div className="mb-6 p-4 bg-gray-100 rounded-lg">
        <h2 className=&quot;text-xl font-semibold mb-2">User Status</h2>
        <p><strong>Has User:</strong> {user ? 'Yes' : 'No'}</p>
        <p><strong>Has Session:</strong> {session ? 'Yes' : 'No'}</p>
        <p><strong>User Error:</strong> {userError ? userError.message : 'None'}</p>
        <p><strong>Session Error:</strong> {sessionError ? sessionError.message : 'None'}</p>
      </div>

      {user && (
        <div className="mb-6 p-4 bg-gray-100 rounded-lg">
          <h2 className=&quot;text-xl font-semibold mb-2">User Details</h2>
          <p><strong>User ID:</strong> {user.id}</p>
          <p><strong>Email:</strong> {user.email}</p>
          <p><strong>Created At:</strong> {new Date(user.created_at).toLocaleString()}</p>
          <p><strong>Last Sign In:</strong> {user.last_sign_in_at ? new Date(user.last_sign_in_at).toLocaleString() : 'Never'}</p>
          <pre className="mt-4 p-2 bg-gray-200 rounded overflow-auto">
            {JSON.stringify(user, null, 2)}
          </pre>
        </div>
      )}

      {session && (
        <div className=&quot;mb-6 p-4 bg-gray-100 rounded-lg">
          <h2 className="text-xl font-semibold mb-2">Session Details</h2>
          <p><strong>Session ID:</strong> {session.access_token.substring(0, 10)}...</p>
          <p><strong>Expires At:</strong> {session.expires_at ? new Date(session.expires_at * 1000).toLocaleString() : 'Not available'}</p>
          <p><strong>Refresh Token:</strong> {session.refresh_token.substring(0, 10)}...</p>
        </div>
      )}

      <div className=&quot;flex gap-4">
        <a href="/dashboard" className=&quot;px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
          Go to Dashboard
        </a>
        <a href="/sign-in" className=&quot;px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
          Go to Sign In
        </a>
      </div>
    </div>
  );
}
