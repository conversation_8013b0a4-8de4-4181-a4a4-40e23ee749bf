import { NextRequest, NextResponse } from "next/server";
import { isAdmin } from "../../../../lib/auth";
import { z } from "zod";
import {
  getApplications,
  approveApplication,
  rejectApplication,
  getApplicationById
} from "../../../../app/dashboard/organizations/apply/actions";

// Query params validation schema
const searchParamsSchema = z.object({
  status: z.enum(['all', 'draft', 'submitted', 'approved', 'rejected']).optional(),
  page: z.coerce.number().int().positive().optional(),
  limit: z.coerce.number().int().positive().max(100).optional(),
  sortBy: z.enum(['created_at', 'updated_at', 'organization_name']).optional(),
  sortDirection: z.enum(['asc', 'desc']).optional()
});

const applicationActionSchema = z.object({
  applicationId: z.string().min(1, "Application ID is required"),
  action: z.enum(['approve', 'reject']),
  reason: z.string().optional()
});

/**
 * GET handler for applications
 */
export async function GET(request: NextRequest) {
  // Check if the current user is an admin
  if (!(await isAdmin())) {
    return NextResponse.json(
      { success: false, message: "Unauthorized access" },
      { status: 403 }
    );
  }

  try {
    const searchParams = request.nextUrl.searchParams;

    // Handle single application fetch if id is provided
    const applicationId = searchParams.get('id');
    if (applicationId) {
      const application = await getApplicationById(applicationId);

      if (!application) {
        return NextResponse.json(
          { success: false, message: "Application not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        application
      });
    }

    // Otherwise, handle listing applications with filtering
    const validatedParams = searchParamsSchema.parse({
      status: searchParams.get('status') || undefined,
      page: searchParams.get('page') || undefined,
      limit: searchParams.get('limit') || undefined,
      sortBy: searchParams.get('sortBy') || undefined,
      sortDirection: searchParams.get('sortDirection') || undefined
    });

    const result = await getApplications({
      status: validatedParams.status || 'all',
      page: validatedParams.page || 1,
      limit: validatedParams.limit || 10,
      sortBy: validatedParams.sortBy || 'updated_at',
      sortDirection: validatedParams.sortDirection || 'desc'
    });

    return NextResponse.json({
      success: true,
      ...result
    });
  } catch (error) {
    console.error("Error in applications API route:", error);

    // Handle validation errors specifically
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid parameters",
          errors: error.errors?.map(e => ({ path: e.path, message.message })) || []
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, message: "An error occurred processing your request" },
      { status: 500 }
    );
  }
}

/**
 * POST handler for application actions (approve/reject)
 */
export async function POST(request: NextRequest) {
  // Check if the current user is an admin
  if (!(await isAdmin())) {
    return NextResponse.json(
      { success: false, message: "Unauthorized access" },
      { status: 403 }
    );
  }

  try {
    const data = await request.json();

    // Validate the action data
    const validatedData = applicationActionSchema.parse(data);

    // Perform the requested action
    switch (validatedData.action) {
      case 'approve': {
        const approveResult = await approveApplication(validatedData.applicationId);
        return NextResponse.json({
          success: approveResult.success,
          message: approveResult.message
        }, { status: approveResult.success ? 200 : 400 });
      }

      case 'reject': {
        // Require reason for rejection
        if (!validatedData.reason) {
          return NextResponse.json(
            { success: false, message: "Reason is required for rejection" },
            { status: 400 }
          );
        }

        const rejectResult = await rejectApplication(
          validatedData.applicationId,
          validatedData.reason
        );

        return NextResponse.json({
          success: rejectResult.success,
          message: rejectResult.message
        }, { status: rejectResult.success ? 200 : 400 });
      }

      default:
        return NextResponse.json(
          { success: false, message: `Unknown action: ${validatedData.action}` },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("Error in applications API POST route:", error);

    // Handle validation errors specifically
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: "Invalid parameters",
          errors: error.errors?.map(e => ({ path: e.path, message.message })) || []
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, message: "An error occurred processing your request" },
      { status: 500 }
    );
  }
}