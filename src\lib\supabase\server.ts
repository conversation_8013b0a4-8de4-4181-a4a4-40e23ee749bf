import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { type Database } from './types'
import { CookieOptions } from '@supabase/ssr'
import { cache } from 'react'
import { validateEnvironment } from './validate-env'
import { logger } from '@/lib/logger';

// Run environment validation on server startup
// This will log any issues with the Supabase environment variables
validateEnvironment()

/**
 * Create Supabase client for database operations and authentication
 * This function is cached to avoid creating multiple clients for the same request
 * It should be used in Server Components, Server Actions, and Route Handlers
 */
export const createClient = cache(async () => {
  const cookieStore = await cookies()

  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  if (!supabaseUrl || !supabaseKey) {
    const missingVars = [];
    if (!supabaseUrl) missingVars.push('NEXT_PUBLIC_SUPABASE_URL');
    if (!supabaseKey) missingVars.push('NEXT_PUBLIC_SUPABASE_ANON_KEY');

    const errorMessage = `Missing Supabase credentials: ${missingVars.join(', ')}. Please check your environment variables.`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }

  // Log the cookies for debugging
  if (process.env.NODE_ENV === 'development') {
    const allCookies = await cookieStore.getAll();
    const cookieNames = allCookies.map(c => c.name);
    logger.info('Server createClient cookies:', cookieNames);
  }

  return createServerClient<Database>(
    supabaseUrl,
    supabaseKey,
    {
      cookies: {
        async getAll() {
          return await cookieStore.getAll()
        },
        async setAll(cookiesToSet) {
          try {
            // Log cookie operations in development
            if (process.env.NODE_ENV === 'development' && cookiesToSet.length > 0) {
              logger.info('Server setting cookies:', cookiesToSet.map(c => c.name));
            }

            for (const { name, value, options } of cookiesToSet) {
              await cookieStore.set(name, value, options)
            }
          } catch (error) {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
            console.warn('Could not set cookies in server component:', error);
          }
        },
      },
    }
  )
})

/**
 * Create Supabase admin client with service role key for privileged operations
 * This should only be used on the server side and for operations that require admin privileges
 * SECURITY WARNING: Never expose this client to the browser or client-side code
 */
export const createAdminClient = cache(async () => {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

  if (!supabaseUrl || !supabaseServiceKey) {
    const missingVars = [];
    if (!supabaseUrl) missingVars.push('NEXT_PUBLIC_SUPABASE_URL');
    if (!supabaseServiceKey) missingVars.push('SUPABASE_SERVICE_ROLE_KEY');

    const errorMessage = `Missing Supabase admin credentials: ${missingVars.join(', ')}. Please check your environment variables.`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }

  return createServerClient<Database>(
    supabaseUrl,
    supabaseServiceKey,
    {
      // No cookies needed for admin operations
      cookies: {
        getAll() { return [] },
        setAll() { /* No-op */ },
      },
    }
  )
})

// Update to async function
export const serverSupabase = async () => await createClient()

// Admin client for privileged operations
export const adminSupabase = async () => await createAdminClient()

// Check Supabase connection and authentication status
export async function checkSupabaseConnection() {
  try {
    const supabase = await createAdminClient()
    const { data, error } = await supabase.from('users').select('count').limit(1)

    if (error) {
      console.error('Supabase connection check failed:', error)
      return {
        success: false,
        error,
        message: `Connection failed: ${error.message}`,
      }
    }

    return {
      success: true,
      message: 'Supabase connection successful',
    }
  } catch (error) {
    console.error('Error checking Supabase connection:', error)
    return {
      success: false,
      error,
      message: error instanceof Error ? error.message : 'Unknown error checking Supabase connection',
    }
  }
}