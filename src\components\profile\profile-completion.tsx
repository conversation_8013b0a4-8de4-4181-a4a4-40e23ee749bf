'use client';

import { useEffect, useState } from 'react';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Info } from 'lucide-react';
import { logger } from '@/lib/logger';
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { createClient } from '@/lib/supabase/client';

interface ProfileCompletionProps {
  userId: string;
}

export function ProfileCompletion({ userId }: ProfileCompletionProps) {
  const [completionPercentage, setCompletionPercentage] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Fetch actual profile completion data
    const fetchCompletionData = async () => {
      setLoading(true);
      setError(null);

      try {
        if (process.env.NODE_ENV === 'development') {

          logger.debug('[DEBUG] ProfileCompletion - Fetching completion data for user:', userId);

        }
        // Skip the API call and calculate locally since we already have the userId
        // This avoids auth state issues between server and client
        const supabase = createClient();

        // Get user data directly from Supabase
        let userProfile;
        const { data: userData, error: userError } = await supabase
          .from('users')
          .select('*')
          .eq('id', userId)
          .single();

        if (userError || !userData) {
          console.error('[DEBUG] ProfileCompletion - Failed to get user data directly:', userError);

          // Try to get by auth_user_id as fallback
          const { data: { user: authUser } } = await supabase.auth.getUser();

          if (authUser) {
            const { data: userByAuthId, error: authIdError } = await supabase
              .from('users')
              .select('*')
              .eq('auth_user_id', authUser.id)
              .single();

            if (authIdError || !userByAuthId) {
              console.error('[DEBUG] ProfileCompletion - Failed to get user by auth_user_id:', authIdError);
              throw new Error('Failed to fetch user data');
            }

            // Use the user found by auth_user_id
            userProfile = userByAuthId;
          } else {
            throw new Error('Failed to fetch user data');
          }
        } else {
          userProfile = userData;
        }

        // Calculate completion percentage locally
        const requiredFields = [
          !!userProfile.first_name,
          !!userProfile.last_name,
          !!userProfile.email,
          !!userProfile.username,
          !!userProfile.gender,
          !!userProfile.country,
          !!userProfile.nationality,
          !!userProfile.contactNo,
          !!userProfile.avatar,
        ];

        const completedFields = requiredFields.filter(Boolean).length;
        const totalFields = requiredFields.length;
        const completionPercentage = Math.round((completedFields / totalFields) * 100);

        if (process.env.NODE_ENV === 'development') {


          logger.debug('[DEBUG] ProfileCompletion - Calculated locally:', completionPercentage);


        }
        setCompletionPercentage(completionPercentage);
      } catch (error) {
        console.error("[DEBUG] ProfileCompletion - Error fetching profile completion data:", error);
        setError('Unable to load profile data');
        // Fallback to a default value if API fails
        setCompletionPercentage(60);
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchCompletionData();
    } else {
      if (process.env.NODE_ENV === 'development') {

        logger.debug('[DEBUG] ProfileCompletion - No userId provided, skipping fetch');

      }
      setLoading(false);
    }
  }, [userId]);

  const getCompletionLabel = () => {
    if (completionPercentage < 70) return "Incomplete";
    if (completionPercentage < 90) return "Almost Complete";
    return "Complete";
  };

  const getCompletionColor = () => {
    if (completionPercentage < 70) return "bg-amber-100 text-amber-800";
    if (completionPercentage < 90) return "bg-blue-100 text-blue-800";
    return "bg-green-100 text-green-800";
  };

  if (loading) {
    return <div className="h-6 w-48 bg-[hsl(var(--muted))] dark:bg-[hsl(var(--dark-muted))] animate-pulse rounded-full"></div>;
  }

  return (
    <div className="flex items-center gap-3">
      <div className="flex flex-col gap-1 w-48">
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Profile completion</span>
          <Badge
            variant="outline"
            className={getCompletionColor()}
          >
            {getCompletionLabel()}
          </Badge>
        </div>
        <Progress value={completionPercentage} className="h-2" />
        {error && <p className="text-xs text-red-500">{error}</p>}
      </div>

      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button className="inline-flex items-center justify-center rounded-full text-muted-foreground hover:bg-accent hover:text-accent-foreground h-6 w-6">
              <Info className="h-4 w-4" />
              <span className="sr-only">Profile completion info</span>
            </button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Complete your profile for better event recommendations</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
}
