'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, CheckCircle2 } from 'lucide-react';
import { applyMigration } from '@/app/actions/apply-migration';

export default function MigrationsPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<{ success: boolean; error?: string } | null>(null);

  const handleApplyMigration = async () => {
    setIsLoading(true);
    setResult(null);
    
    try {
      const migrationResult = await applyMigration('0018_fix_profile_activity.sql');
      setResult(migrationResult);
    } catch (error) {
      setResult({ 
        success: false, 
        error instanceof Error ? error.message : 'Unknown error occurred' 
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className=&quot;text-2xl font-bold mb-6">Database Migrations</h1>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Fix Profile Activity Tracking</CardTitle>
          <CardDescription>
            This migration will fix the profile activity tracking system by aligning the database schema
            with the application code and setting up automatic activity logging.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className=&quot;text-sm text-muted-foreground mb-4">
            This will:
          </p>
          <ul className="list-disc pl-5 text-sm text-muted-foreground space-y-1 mb-4">
            <li>Add missing columns to the profile_activity table</li>
            <li>Create a trigger function to automatically log profile changes</li>
            <li>Set up a trigger on the users table</li>
          </ul>
        </CardContent>
        <CardFooter className=&quot;flex flex-col items-start gap-4">
          <Button 
            onClick={handleApplyMigration} 
            disabled={isLoading}
          >
            {isLoading ? 'Applying Migration...' : 'Apply Migration'}
          </Button>
          
          {result && (
            <Alert variant={result.success ? "default" : "destructive"}>
              {result.success ? (
                <CheckCircle2 className="h-4 w-4" />
              ) : (
                <AlertCircle className=&quot;h-4 w-4" />
              )}
              <AlertTitle>
                {result.success ? 'Migration Applied Successfully' : 'Migration Failed'}
              </AlertTitle>
              <AlertDescription>
                {result.success 
                  ? 'The profile activity tracking system has been fixed. Profile updates will now be logged automatically.'
                  : `Error: ${result.error}`
                }
              </AlertDescription>
            </Alert>
          )}
        </CardFooter>
      </Card>
    </div>
  );
}
