'use client'

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { createClient } from "@/lib/supabase/client"
import { useEffect, useState } from "react"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"

interface Payment {
  id: string
  amount: number
  status: string
  created_at: string
  payment_method: string
}

interface RecentPaymentsProps {
  userId: string
  className?: string
}

export function RecentPayments({ userId, className }: RecentPaymentsProps) {
  const [payments, setPayments] = useState<Payment[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchPayments = async () => {
      setLoading(true)
      const supabase = createClient()

      // First get the user's registrations
      const { data: registrations } = await supabase
        .from('registrations')
        .select('id')
        .eq('user_id', userId)

      if (!registrations || registrations.length === 0) {
        setLoading(false)
        return
      }

      const registrationIds = registrations.map(reg => reg.id)

      // Then get payments for those registrations
      const { data } = await supabase
        .from('payments')
        .select('*')
        .in('registration_id', registrationIds)
        .order('created_at', { ascending: false })
        .limit(5)

      setPayments(data || [])
      setLoading(false)
    }

    fetchPayments()
  }, [userId])

  function formatCurrency(amount: number) {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'MYR'
    }).format(amount)
  }

  function getStatusColor(status: string) {
    switch (status.toLowerCase()) {
      case 'paid':
        return 'bg-green-100 text-green-800'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800'
      case 'failed':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Recent Payments</CardTitle>
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="space-y-2">
            {Array.from({ length: 3 }).map((_, i) => (
              <div key={i} className=&quot;h-12 bg-gray-100 animate-pulse rounded-md" />
            ))}
          </div>
        ) : payments.length === 0 ? (
          <p className="text-sm text-muted-foreground">No payment records found.</p>
        ) : (
          <div className=&quot;space-y-4">
            {payments.map((payment) => (
              <div key={payment.id} className="flex items-center justify-between">
                <div className=&quot;space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {formatCurrency(payment.amount)}
                  </p>
                  <p className=&quot;text-xs text-muted-foreground">
                    {format(new Date(payment.created_at), 'PPP')}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  <p className=&quot;text-xs text-muted-foreground">
                    {payment.payment_method}
                  </p>
                  <Badge variant="outline" className={getStatusColor(payment.status)}>
                    {payment.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
} 