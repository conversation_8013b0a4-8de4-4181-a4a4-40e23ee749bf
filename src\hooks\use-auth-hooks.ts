'use client';

import { useState, useCallback, useEffect } from 'react';
import { useAuth } from '@/contexts/auth-context';
import { createClient } from '@/lib/supabase/client';
import { useRouter } from 'next/navigation';
import { Provider } from '@supabase/supabase-js';
import { getAuthCallbackUrl } from '@/utils/url-utilities';
import { logger } from '@/lib/logger';

/**
 * Hook for handling sign-in with email and password
 * @returns Sign-in function and loading state
 */
export function useEmailSignIn() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();
  const { refreshSession } = useAuth();

  const signIn = useCallback(async (email: string, password: string, redirectUrl?: string) => {
    setLoading(true);
    setError(null);

    try {
      const supabase = createClient();

      // Sign in with email and password
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw error;
      }

      // Refresh the session to ensure we have the latest user data
      await refreshSession();

      // Redirect to the specified URL or dashboard
      const finalRedirectUrl = redirectUrl || '/dashboard';

      // Create a URL object for the redirect
      const urlObj = new URL(
        finalRedirectUrl.startsWith('http')
          ? finalRedirectUrl
          : `${window.location.origin}${finalRedirectUrl.startsWith('/') ? finalRedirectUrl : `/${finalRedirectUrl}`}`
      );

      // Add no_redirect=true to prevent redirect loops
      urlObj.searchParams.set('no_redirect', 'true');

      // Add a timestamp to prevent caching issues
      urlObj.searchParams.set('_ts', Date.now().toString());

      // Force a small delay to ensure the session is properly set
      setTimeout(() => {
        // Use window.location for a full page refresh to ensure auth state is updated
        window.location.href = urlObj.toString();
      }, 500);

      return true;
    } catch (err: unknown) {
      console.error('Sign-in error:', err);
      setError(err.message || 'An error occurred during sign-in');
      return false;
    } finally {
      setLoading(false);
    }
  }, [refreshSession, router]);

  return { signIn, loading, error };
}

/**
 * Hook for handling sign-up with email and password
 * @returns Sign-up function and loading state
 */
export function useEmailSignUp() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const signUp = useCallback(async (email: string, password: string, redirectUrl?: string) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      const supabase = createClient();

      // Get the auth callback URL
      const callbackUrl = getAuthCallbackUrl();

      // Create a URL object from the callback URL string
      const callbackUrlObj = new URL(callbackUrl);

      // Add the redirect_to parameter to the callback URL
      if (redirectUrl) {
        // Prevent redirect loops with auth callback
        if (redirectUrl.includes('/auth/callback')) {
          console.warn('Detected potential auth redirect loop in signUp, redirecting to dashboard');
          redirectUrl = '/dashboard';
        }
        callbackUrlObj.searchParams.set('redirect_to', redirectUrl);
      }

      // Sign up with email and password
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: callbackUrlObj.toString(),
        },
      });

      if (error) {
        throw error;
      }

      setSuccess(true);
      return true;
    } catch (err: unknown) {
      console.error('Sign-up error:', err);
      setError(err.message || 'An error occurred during sign-up');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  return { signUp, loading, error, success };
}

/**
 * Hook for handling sign-in with OAuth providers
 * @returns Sign-in function and loading state
 */
export function useOAuthSignIn() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const signIn = useCallback(async (provider: Provider, redirectUrl?: string) => {
    setLoading(true);
    setError(null);

    try {
      const supabase = createClient();

      // Get the auth callback URL - this should be the production URL in production
      const callbackUrl = getAuthCallbackUrl();
      logger.info('OAuth sign-in using callback URL:', callbackUrl);
      // Create a URL object from the callback URL string
      const callbackUrlObj = new URL(callbackUrl);

      // Add the redirect_to parameter to the callback URL
      if (redirectUrl) {
        // Prevent redirect loops with auth callback
        if (redirectUrl.includes('/auth/callback')) {
          console.warn('Detected potential auth redirect loop in OAuth signIn, redirecting to dashboard');
          redirectUrl = '/dashboard';
        }
        callbackUrlObj.searchParams.set('redirect_to', redirectUrl);
      }

      // Store the final redirect URL in localStorage to ensure it's available after auth
      if (redirectUrl && redirectUrl !== '/dashboard') {
        try {
          localStorage.setItem('auth_redirect_url', redirectUrl);
          logger.info('Stored redirect URL in localStorage:', redirectUrl);
        } catch (e) {
          console.warn('Failed to store redirect URL in localStorage:', e);
        }
      }

      // Ensure we're using the full URL for the redirectTo option
      const finalCallbackUrl = callbackUrlObj.toString();
      logger.info('Final OAuth callback URL with params:', finalCallbackUrl);
      // Sign in with OAuth
      const options: {
        redirectTo: string;
        queryParams?: { [key: string]: string };
      } = {
        redirectTo: finalCallbackUrl,
      };

      // Force Google to show the account selection screen every time
      if (provider === 'google') {
        options.queryParams = {
          prompt: 'select_account'
        };
      }

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider,
        options,
      });

      if (error) {
        throw error;
      }

      logger.info('OAuth sign-in initiated successfully');
      // Note: We won't reach this point in normal flow as the browser will be redirected
      return true;
    } catch (err: unknown) {
      console.error('OAuth sign-in error:', err);
      setError(err.message || 'An error occurred during sign-in');
      setLoading(false);
      return false;
    }
  }, []);

  return { signIn, loading, error };
}

/**
 * Hook for handling sign-out
 * @returns Sign-out function and loading state
 */
export function useSignOut() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const signOut = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Call the server-side sign-out API
      const response = await fetch('/api/auth/sign-out', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to sign out');
      }

      // Also try to sign out with the client-side Supabase SDK as a fallback
      try {
        const supabase = createClient();
        await supabase.auth.signOut();
      } catch (e) {
        console.warn('Client-side sign-out failed:', e);
      }

      // Redirect to home page
      router.push('/');
      return true;
    } catch (err: unknown) {
      console.error('Sign-out error:', err);
      setError(err.message || 'An error occurred during sign-out');
      return false;
    } finally {
      setLoading(false);
    }
  }, [router]);

  return { signOut, loading, error };
}
