#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const rootDir = path.resolve(__dirname, '..');
const srcDir = path.join(rootDir, 'src');

// Statistics
let stats = {
  filesProcessed: 0,
  filesModified: 0,
  consoleLogsReplaced: 0,
  unusedVarsRemoved: 0,
  quotesFixed: 0,
  shorthandFixed: 0,
  constFixed: 0,
  errors: 0,
};

// Patterns for fixes
const patterns = {
  // Console.log statements (keep console.error and console.warn)
  consoleLog: /^(\s*)console\.log\s*\((.*?)\)\s*;?\s*$/gm,
  
  // Unused imports/variables (simple cases)
  unusedImport: /^import\s+\{[^}]*\b(\w+)\b[^}]*\}\s+from\s+['""][^'"]*['""];?\s*$/gm,
  
  // Unescaped quotes in JSX
  unescapedQuotes: /(\w+)="([^"]*)"([^"]*)"([^"]*)"/g,
  
  // Object shorthand opportunities
  objectShorthand: /(\w+):\s*\1\b/g,
  
  // let that should be const
  letToConst: /^(\s*)let\s+(\w+)\s*=\s*([^;]+);?\s*$/gm,
  
  // Logger import pattern
  loggerImport: /import.*logger.*from.*['"]@\/lib\/logger['"];?/,
};

/**
 * Check if file should be processed
 */
function shouldProcessFile(filePath) {
  const ext = path.extname(filePath);
  return ['.ts', '.tsx', '.js', '.jsx'].includes(ext) && 
         !filePath.includes('node_modules') && 
         !filePath.includes('.next') &&
         !filePath.includes('dist') &&
         !filePath.includes('build');
}

/**
 * Find all files to process
 */
function findFiles(dir) {
  let results = [];
  
  try {
    const files = fs.readdirSync(dir);
    
    for (const file of files) {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        results = results.concat(findFiles(filePath));
      } else if (shouldProcessFile(filePath)) {
        results.push(filePath);
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dir}:`, error.message);
    stats.errors++;
  }
  
  return results;
}

/**
 * Fix console.log statements
 */
function fixConsoleLogs(content) {
  let modified = false;
  let replacements = 0;
  
  // Check if logger import exists
  const hasLoggerImport = patterns.loggerImport.test(content);
  
  // Replace console.log with logger.info
  const newContent = content.replace(patterns.consoleLog, (match, indent, args) => {
    replacements++;
    modified = true;
    return `${indent}logger.info(${args});`;
  });
  
  // Add logger import if needed and console.log was replaced
  if (replacements > 0 && !hasLoggerImport) {
    const lines = newContent.split('\n');
    let insertIndex = 0;
    
    // Find where to insert logger import (after other imports)
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].match(/^import.*from/)) {
        insertIndex = i + 1;
      } else if (lines[i].trim() === '' && insertIndex > 0) {
        break;
      }
    }
    
    lines.splice(insertIndex, 0, "import { logger } from '@/lib/logger';");
    stats.consoleLogsReplaced += replacements;
    return lines.join('\n');
  }
  
  if (modified) {
    stats.consoleLogsReplaced += replacements;
  }
  
  return modified ? newContent : content;
}

/**
 * Fix unescaped quotes in JSX
 */
function fixUnescapedQuotes(content) {
  let modified = false;
  
  // Fix quotes in JSX attributes and content
  const newContent = content
    .replace(/(\w+)="([^"]*)"([^"]*)"([^"]*)"/g, (match, attr, p1, p2, p3) => {
      modified = true;
      stats.quotesFixed++;
      return `${attr}="${p1}&quot;${p2}&quot;${p3}"`;
    })
    .replace(/>\s*"([^"]*)"([^"]*)"([^"]*)\s*</g, (match, p1, p2, p3) => {
      modified = true;
      stats.quotesFixed++;
      return `>&quot;${p1}&quot;${p2}&quot;${p3}<`;
    });
  
  return modified ? newContent : content;
}

/**
 * Fix object shorthand
 */
function fixObjectShorthand(content) {
  let modified = false;
  
  const newContent = content.replace(/(\w+):\s*\1\b/g, (match, prop) => {
    modified = true;
    stats.shorthandFixed++;
    return prop;
  });
  
  return modified ? newContent : content;
}

/**
 * Fix let to const where appropriate
 */
function fixLetToConst(content) {
  let modified = false;
  
  const newContent = content.replace(/^(\s*)let\s+(\w+)\s*=\s*([^;]+);?\s*$/gm, (match, indent, varName, value) => {
    // Simple heuristic: if it's a simple assignment and not reassigned later
    const reassignmentPattern = new RegExp(`\\b${varName}\\s*=`, 'g');
    const matches = content.match(reassignmentPattern);
    
    if (matches && matches.length === 1) { // Only the initial assignment
      modified = true;
      stats.constFixed++;
      return `${indent}const ${varName} = ${value};`;
    }
    
    return match;
  });
  
  return modified ? newContent : content;
}

/**
 * Process a single file
 */
function processFile(filePath) {
  try {
    stats.filesProcessed++;
    
    let content = fs.readFileSync(filePath, 'utf8');
    const originalContent = content;
    
    // Apply fixes
    content = fixConsoleLogs(content);
    content = fixUnescapedQuotes(content);
    content = fixObjectShorthand(content);
    content = fixLetToConst(content);
    
    // Write back if modified
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      stats.filesModified++;
      console.log(`✅ Fixed: ${path.relative(rootDir, filePath)}`);
    }
    
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
    stats.errors++;
  }
}

/**
 * Main execution
 */
function main() {
  console.log('🚀 Starting automated ESLint fixes...');
  console.log('📁 Source directory:', srcDir);
  console.log('');
  
  const files = findFiles(srcDir);
  console.log(`📄 Found ${files.length} files to check`);
  console.log('');
  
  files.forEach(processFile);
  
  console.log('');
  console.log('📊 SUMMARY:');
  console.log(`   Files processed: ${stats.filesProcessed}`);
  console.log(`   Files modified: ${stats.filesModified}`);
  console.log(`   Console.log statements replaced: ${stats.consoleLogsReplaced}`);
  console.log(`   Unescaped quotes fixed: ${stats.quotesFixed}`);
  console.log(`   Object shorthand fixed: ${stats.shorthandFixed}`);
  console.log(`   let → const conversions: ${stats.constFixed}`);
  console.log(`   Errors: ${stats.errors}`);
  console.log('');
  
  if (stats.errors === 0) {
    console.log('✅ All fixes completed successfully!');
  } else {
    console.log('⚠️  Some errors occurred during processing.');
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = { main, processFile };
